"""
Data Validation Script
Validates the quality and completeness of the generated Excel data
"""

import pandas as pd
import os
import glob
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_excel_file(filepath: str) -> dict:
    """Validate an Excel file and return quality metrics"""
    
    logger.info(f"Validating Excel file: {filepath}")
    
    try:
        # Read the main consolidated sheet
        df = pd.read_excel(filepath, sheet_name='Consolidated_Data')
        
        validation_results = {
            'file_path': filepath,
            'total_records': len(df),
            'validation_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'issues': [],
            'quality_score': 0
        }
        
        # Check required columns
        required_columns = ['Year', 'Country', 'Manufacturer', 'VehicleType', 
                          'ProductionVolume', 'TariffRate_USA', 'SteelPrice', 
                          'EV_Share', 'ImportVolume']
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            validation_results['issues'].append(f"Missing columns: {missing_columns}")
        
        # Check data completeness
        for col in required_columns:
            if col in df.columns:
                null_count = df[col].isnull().sum()
                null_percentage = (null_count / len(df)) * 100
                
                if null_percentage > 50:
                    validation_results['issues'].append(f"Column '{col}' has {null_percentage:.1f}% null values")
        
        # Check year range
        if 'Year' in df.columns:
            years = df['Year'].dropna().unique()
            year_range = f"{min(years)}-{max(years)}" if len(years) > 1 else str(years[0])
            validation_results['year_range'] = year_range
            validation_results['years_covered'] = len(years)
        
        # Check country coverage
        if 'Country' in df.columns:
            countries = df['Country'].dropna().unique()
            validation_results['countries_covered'] = len(countries)
            validation_results['top_countries'] = list(countries[:10])
        
        # Check manufacturer coverage
        if 'Manufacturer' in df.columns:
            manufacturers = df['Manufacturer'].dropna().unique()
            validation_results['manufacturers_covered'] = len(manufacturers)
            validation_results['top_manufacturers'] = list(manufacturers[:10])
        
        # Check production volume data
        if 'ProductionVolume' in df.columns:
            prod_data = df['ProductionVolume'].dropna()
            if len(prod_data) > 0:
                validation_results['avg_production'] = int(prod_data.mean())
                validation_results['total_production'] = int(prod_data.sum())
                validation_results['max_production'] = int(prod_data.max())
        
        # Check EV share data
        if 'EV_Share' in df.columns:
            ev_data = df['EV_Share'].dropna()
            ev_numeric = []
            for val in ev_data:
                if isinstance(val, str) and '%' in val:
                    try:
                        ev_numeric.append(float(val.replace('%', '')))
                    except:
                        pass
                elif isinstance(val, (int, float)):
                    ev_numeric.append(float(val))
            
            if ev_numeric:
                validation_results['avg_ev_share'] = f"{sum(ev_numeric)/len(ev_numeric):.1f}%"
                validation_results['max_ev_share'] = f"{max(ev_numeric):.1f}%"
        
        # Calculate quality score
        quality_score = 100
        quality_score -= len(validation_results['issues']) * 10  # -10 points per issue
        quality_score -= len(missing_columns) * 20  # -20 points per missing column
        
        # Bonus points for good coverage
        if validation_results.get('countries_covered', 0) >= 10:
            quality_score += 5
        if validation_results.get('manufacturers_covered', 0) >= 5:
            quality_score += 5
        if validation_results.get('years_covered', 0) >= 5:
            quality_score += 5
        
        validation_results['quality_score'] = max(0, min(100, quality_score))
        
        return validation_results
        
    except Exception as e:
        logger.error(f"Error validating file {filepath}: {e}")
        return {
            'file_path': filepath,
            'error': str(e),
            'quality_score': 0
        }

def validate_all_excel_files():
    """Validate all Excel files in the output directory"""
    
    excel_dir = "output/excel"
    if not os.path.exists(excel_dir):
        logger.error(f"Excel directory not found: {excel_dir}")
        return
    
    # Find all Excel files
    excel_files = glob.glob(os.path.join(excel_dir, "*.xlsx"))
    
    if not excel_files:
        logger.warning("No Excel files found to validate")
        return
    
    logger.info(f"Found {len(excel_files)} Excel files to validate")
    
    all_results = []
    
    for excel_file in excel_files:
        result = validate_excel_file(excel_file)
        all_results.append(result)
    
    # Print summary report
    print("\n" + "="*80)
    print("📊 DATA VALIDATION REPORT")
    print("="*80)
    
    for result in all_results:
        filename = os.path.basename(result['file_path'])
        print(f"\n📄 File: {filename}")
        print(f"   Quality Score: {result.get('quality_score', 0)}/100")
        print(f"   Total Records: {result.get('total_records', 0):,}")
        
        if 'year_range' in result:
            print(f"   Year Range: {result['year_range']}")
        if 'countries_covered' in result:
            print(f"   Countries: {result['countries_covered']}")
        if 'manufacturers_covered' in result:
            print(f"   Manufacturers: {result['manufacturers_covered']}")
        
        if 'avg_production' in result:
            print(f"   Avg Production: {result['avg_production']:,} units")
        if 'total_production' in result:
            print(f"   Total Production: {result['total_production']:,} units")
        
        if 'avg_ev_share' in result:
            print(f"   Avg EV Share: {result['avg_ev_share']}")
        
        if result.get('issues'):
            print(f"   ⚠️  Issues:")
            for issue in result['issues']:
                print(f"      - {issue}")
        
        if result.get('error'):
            print(f"   ❌ Error: {result['error']}")
    
    # Overall summary
    valid_files = [r for r in all_results if 'error' not in r]
    if valid_files:
        avg_quality = sum(r['quality_score'] for r in valid_files) / len(valid_files)
        total_records = sum(r.get('total_records', 0) for r in valid_files)
        
        print(f"\n📈 OVERALL SUMMARY:")
        print(f"   Files Validated: {len(valid_files)}/{len(all_results)}")
        print(f"   Average Quality Score: {avg_quality:.1f}/100")
        print(f"   Total Records: {total_records:,}")
        
        # Quality assessment
        if avg_quality >= 80:
            print(f"   ✅ Data Quality: Excellent")
        elif avg_quality >= 60:
            print(f"   ⚠️  Data Quality: Good")
        else:
            print(f"   ❌ Data Quality: Needs Improvement")
    
    print("="*80)

def show_sample_data():
    """Show sample data from the most recent Excel file"""
    
    excel_dir = "output/excel"
    excel_files = glob.glob(os.path.join(excel_dir, "automotive_data_unified_*.xlsx"))
    
    if not excel_files:
        logger.warning("No unified Excel files found")
        return
    
    # Get the most recent file
    latest_file = max(excel_files, key=os.path.getctime)
    
    try:
        df = pd.read_excel(latest_file, sheet_name='Consolidated_Data')
        
        print("\n" + "="*80)
        print("📋 SAMPLE DATA FROM LATEST FILE")
        print("="*80)
        print(f"File: {os.path.basename(latest_file)}")
        print(f"Total Records: {len(df):,}")
        print("\nFirst 10 records:")
        print(df.head(10).to_string(index=False))
        
        print("\nColumn Summary:")
        for col in df.columns:
            non_null = df[col].count()
            null_pct = ((len(df) - non_null) / len(df)) * 100
            print(f"  {col}: {non_null:,} values ({null_pct:.1f}% null)")
        
        print("="*80)
        
    except Exception as e:
        logger.error(f"Error reading sample data: {e}")

if __name__ == "__main__":
    print("🔍 Starting data validation...")
    validate_all_excel_files()
    show_sample_data()
    print("\n✅ Validation completed!")
