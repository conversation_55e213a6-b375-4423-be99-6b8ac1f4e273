[{"country_region": "Argentina", "cars": 93001, "commercialvehicles": "164186", "total": 257187, "percent_change": -18.0, "year": 2020}, {"country_region": "Austria", "cars": 104544, "commercialvehicles": "-", "total": 104544, "percent_change": -42.0, "year": 2020}, {"country_region": "Belgium", "cars": 237057, "commercialvehicles": "30403", "total": 267460, "percent_change": -6.0, "year": 2020}, {"country_region": "Brazil", "cars": 1608870, "commercialvehicles": "405185", "total": 2014055, "percent_change": -32.0, "year": 2020}, {"country_region": "Canada", "cars": 327681, "commercialvehicles": "1048942", "total": 1376623, "percent_change": -28.0, "year": 2020}, {"country_region": "China", "cars": 19994081, "commercialvehicles": "5231161", "total": 25225242, "percent_change": -2.0, "year": 2020}, {"country_region": "Czech Republic", "cars": 1152901, "commercialvehicles": "6250", "total": 1159151, "percent_change": -19.0, "year": 2020}, {"country_region": "Egypt", "cars": 23754, "commercialvehicles": "-", "total": 23754, "percent_change": 28.0, "year": 2020}, {"country_region": "Finland", "cars": 86270, "commercialvehicles": "-", "total": 86270, "percent_change": -25.0, "year": 2020}, {"country_region": "France", "cars": 927718, "commercialvehicles": "388653", "total": 1316371, "percent_change": -39.0, "year": 2020}, {"country_region": "Germany", "cars": 3515372, "commercialvehicles": "227082", "total": 3742454, "percent_change": -24.0, "year": 2020}, {"country_region": "Hungary", "cars": 406497, "commercialvehicles": "-", "total": 406497, "percent_change": -18.0, "year": 2020}, {"country_region": "India", "cars": 2851268, "commercialvehicles": "543178", "total": 3394446, "percent_change": -25.0, "year": 2020}, {"country_region": "Indonesia", "cars": 551400, "commercialvehicles": "139886", "total": 691286, "percent_change": -46.0, "year": 2020}, {"country_region": "Iran", "cars": 826210, "commercialvehicles": "54787", "total": 880997, "percent_change": 7.0, "year": 2020}, {"country_region": "Italy", "cars": 451826, "commercialvehicles": "325339", "total": 777165, "percent_change": -15.0, "year": 2020}, {"country_region": "Japan", "cars": 6960025, "commercialvehicles": "1107532", "total": 8067557, "percent_change": -17.0, "year": 2020}, {"country_region": "Kazakhstan", "cars": 64790, "commercialvehicles": "10041", "total": 74831, "percent_change": 51.0, "year": 2020}, {"country_region": "Malaysia", "cars": 457755, "commercialvehicles": "27431", "total": 485186, "percent_change": -15.0, "year": 2020}, {"country_region": "Morocco", "cars": 221299, "commercialvehicles": "27131", "total": 248430, "percent_change": -38.0, "year": 2020}, {"country_region": "Mexico", "cars": 967479, "commercialvehicles": "2209121", "total": 3176600, "percent_change": -21.0, "year": 2020}, {"country_region": "Poland", "cars": 278900, "commercialvehicles": "172482", "total": 451382, "percent_change": -31.0, "year": 2020}, {"country_region": "Portugal", "cars": 211281, "commercialvehicles": "52955", "total": 264236, "percent_change": -24.0, "year": 2020}, {"country_region": "Romania", "cars": 438107, "commercialvehicles": "-", "total": 438107, "percent_change": -11.0, "year": 2020}, {"country_region": "Russia", "cars": 1260517, "commercialvehicles": "174818", "total": 1435335, "percent_change": -17.0, "year": 2020}, {"country_region": "Serbia", "cars": 23272, "commercialvehicles": "103", "total": 23375, "percent_change": -33.0, "year": 2020}, {"country_region": "Slovakia", "cars": 985000, "commercialvehicles": "-", "total": 985000, "percent_change": -11.0, "year": 2020}, {"country_region": "Slovenia", "cars": 141714, "commercialvehicles": "-", "total": 141714, "percent_change": -29.0, "year": 2020}, {"country_region": "South Africa", "cars": 238216, "commercialvehicles": "209002", "total": 447218, "percent_change": -29.0, "year": 2020}, {"country_region": "South Korea", "cars": 3211706, "commercialvehicles": "295068", "total": 3506774, "percent_change": -11.0, "year": 2020}, {"country_region": "Spain", "cars": 1800664, "commercialvehicles": "467521", "total": 2268185, "percent_change": -20.0, "year": 2020}, {"country_region": "Taiwan", "cars": 180967, "commercialvehicles": "64648", "total": 245615, "percent_change": -2.0, "year": 2020}, {"country_region": "Thailand", "cars": 537633, "commercialvehicles": "889441", "total": 1427074, "percent_change": -29.0, "year": 2020}, {"country_region": "Turkey", "cars": 855043, "commercialvehicles": "442835", "total": 1297878, "percent_change": -11.0, "year": 2020}, {"country_region": "Ukraine", "cars": 4202, "commercialvehicles": "750", "total": 4952, "percent_change": -32.0, "year": 2020}, {"country_region": "United Kingdom", "cars": 920928, "commercialvehicles": "66116", "total": 987044, "percent_change": -29.0, "year": 2020}, {"country_region": "USA", "cars": 1926795, "commercialvehicles": "6895604", "total": 8822399, "percent_change": -19.0, "year": 2020}, {"country_region": "Uzbekistan", "cars": 280080, "commercialvehicles": "-", "total": 280080, "percent_change": 3.0, "year": 2020}, {"country_region": "Others", "cars": 709633, "commercialvehicles": "109475", "total": 819108, "percent_change": null, "year": 2020}, {"country_region": "Total", "cars": 55834456, "commercialvehicles": "21787126", "total": 77621582, "percent_change": -16.0, "year": 2020}]