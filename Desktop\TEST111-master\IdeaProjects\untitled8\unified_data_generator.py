"""
Unified Data Generator for Automotive Industry
Creates a single Excel file with the exact format requested:
Year | Country | Manufacturer | VehicleType | ProductionVolume | TariffRate_USA | SteelPrice | EV_Share | ImportVolume
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging
import random
import os

from enhanced_config import (
    UNIFIED_EXCEL_CONFIG, MANUFACTURER_MAPPING, TRADE_DATA_CONFIG, 
    STEEL_PRICE_DATA, EXCEL_DIR
)
from data_sources.oica_scraper_enhanced import EnhancedOICAScraper

class UnifiedAutomotiveDataGenerator:
    """Generates unified automotive data in the exact format requested"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.unified_data = []
        
    def generate_unified_dataset(self, years=None):
        """Generate the complete unified dataset"""
        if years is None:
            years = [2023, 2024]
        
        self.logger.info(f"Generating unified automotive dataset for years: {years}")
        
        # Step 1: Get OICA production data
        oica_data = self._get_oica_data(years)
        
        # Step 2: Transform OICA data to unified format
        for year in years:
            year_data = [d for d in oica_data if d.get('year') == year]
            unified_year_data = self._transform_oica_to_unified(year_data, year)
            self.unified_data.extend(unified_year_data)
        
        # Step 3: Add missing data with intelligent estimates
        self._enrich_with_estimates()
        
        # Step 4: Create DataFrame
        df = pd.DataFrame(self.unified_data)
        
        self.logger.info(f"Generated {len(df)} unified records")
        return df
    
    def _get_oica_data(self, years):
        """Get OICA production data"""
        try:
            scraper = EnhancedOICAScraper()
            oica_data = scraper.scrape_multiple_years(years)
            self.logger.info(f"Retrieved {len(oica_data)} OICA records")
            return oica_data
        except Exception as e:
            self.logger.error(f"Failed to get OICA data: {e}")
            return self._create_sample_oica_data(years)
    
    def _create_sample_oica_data(self, years):
        """Create sample OICA data if scraping fails"""
        sample_countries = [
            {'country_region': 'China', 'cars': 21000000, 'commercial_vehicles': 3500000},
            {'country_region': 'USA', 'cars': 2800000, 'commercial_vehicles': 8200000},
            {'country_region': 'Japan', 'cars': 7800000, 'commercial_vehicles': 1400000},
            {'country_region': 'Germany', 'cars': 3200000, 'commercial_vehicles': 400000},
            {'country_region': 'India', 'cars': 4500000, 'commercial_vehicles': 1200000},
            {'country_region': 'South Korea', 'cars': 3100000, 'commercial_vehicles': 350000},
            {'country_region': 'Mexico', 'cars': 2800000, 'commercial_vehicles': 450000},
            {'country_region': 'Brazil', 'cars': 1900000, 'commercial_vehicles': 300000},
            {'country_region': 'Spain', 'cars': 1800000, 'commercial_vehicles': 200000},
            {'country_region': 'France', 'cars': 1600000, 'commercial_vehicles': 180000}
        ]
        
        sample_data = []
        for year in years:
            for country_data in sample_countries:
                # Add some year-over-year variation
                variation = random.uniform(0.95, 1.05) if year > min(years) else 1.0
                
                sample_data.append({
                    'country_region': country_data['country_region'],
                    'cars': int(country_data['cars'] * variation),
                    'commercial_vehicles': int(country_data['commercial_vehicles'] * variation),
                    'total': int((country_data['cars'] + country_data['commercial_vehicles']) * variation),
                    'year': year,
                    'source': 'Sample'
                })
        
        return sample_data
    
    def _transform_oica_to_unified(self, oica_data, year):
        """Transform OICA data to unified format"""
        unified_records = []
        
        for record in oica_data:
            country = self._standardize_country_name(record.get('country_region', ''))
            
            if country == 'TOTAL' or not country:
                continue
            
            # Get manufacturers for this country
            manufacturers = MANUFACTURER_MAPPING['country_to_manufacturers'].get(country, ['Unknown'])
            
            # Create records for each manufacturer
            for manufacturer in manufacturers:
                # Calculate manufacturer's share of country production
                manufacturer_share = self._get_manufacturer_share(manufacturer, country)
                
                # Passenger vehicles
                passenger_volume = int((record.get('cars', 0) or 0) * manufacturer_share)
                if passenger_volume > 0:
                    unified_records.append(self._create_unified_record(
                        year=year,
                        country=country,
                        manufacturer=manufacturer,
                        vehicle_type='Passenger',
                        production_volume=passenger_volume
                    ))
                
                # Commercial vehicles
                commercial_volume = int((record.get('commercial_vehicles', 0) or 0) * manufacturer_share)
                if commercial_volume > 0:
                    unified_records.append(self._create_unified_record(
                        year=year,
                        country=country,
                        manufacturer=manufacturer,
                        vehicle_type='Commercial',
                        production_volume=commercial_volume
                    ))
        
        return unified_records
    
    def _create_unified_record(self, year, country, manufacturer, vehicle_type, production_volume):
        """Create a unified record with all required fields"""
        return {
            'Year': year,
            'Country': country,
            'Manufacturer': manufacturer,
            'VehicleType': vehicle_type,
            'ProductionVolume': production_volume,
            'TariffRate_USA': self._get_tariff_rate(country),
            'SteelPrice': self._get_steel_price(year),
            'EV_Share': self._get_ev_share(manufacturer, vehicle_type),
            'ImportVolume': self._estimate_import_volume(country, manufacturer, production_volume)
        }
    
    def _standardize_country_name(self, country_name):
        """Standardize country names"""
        country_mapping = {
            'USA': 'USA',
            'United States': 'USA',
            'US': 'USA',
            'China': 'China',
            'Japan': 'Japan',
            'Germany': 'Germany',
            'India': 'India',
            'South Korea': 'South Korea',
            'Korea': 'South Korea',
            'Rep. of Korea': 'South Korea',
            'Mexico': 'Mexico',
            'Brazil': 'Brazil',
            'Spain': 'Spain',
            'France': 'France',
            'United Kingdom': 'United Kingdom',
            'UK': 'United Kingdom',
            'Italy': 'Italy',
            'Canada': 'Canada',
            'Turkey': 'Turkey',
            'Thailand': 'Thailand',
            'Czech Republic': 'Czech Republic',
            'Poland': 'Poland',
            'Russia': 'Russia',
            'Iran': 'Iran',
            'Indonesia': 'Indonesia',
            'Argentina': 'Argentina',
            'Belgium': 'Belgium',
            'Slovakia': 'Slovakia',
            'Hungary': 'Hungary',
            'Sweden': 'Sweden',
            'Austria': 'Austria',
            'Netherlands': 'Netherlands',
            'Romania': 'Romania',
            'Ukraine': 'Ukraine',
            'South Africa': 'South Africa',
            'Morocco': 'Morocco',
            'Egypt': 'Egypt',
            'Malaysia': 'Malaysia',
            'Taiwan': 'Taiwan',
            'Australia': 'Australia',
            'Slovenia': 'Slovenia',
            'Finland': 'Finland',
            'Portugal': 'Portugal',
            'Serbia': 'Serbia',
            'Uzbekistan': 'Uzbekistan'
        }
        
        return country_mapping.get(country_name, country_name)
    
    def _get_manufacturer_share(self, manufacturer, country):
        """Get manufacturer's market share in country"""
        # Simplified market share logic
        manufacturers_in_country = MANUFACTURER_MAPPING['country_to_manufacturers'].get(country, [manufacturer])
        
        if len(manufacturers_in_country) == 1:
            return 1.0
        
        # Major manufacturers get larger shares
        major_manufacturers = ['Toyota', 'Volkswagen', 'General Motors', 'Ford', 'Hyundai']
        
        if manufacturer in major_manufacturers:
            return 0.4 / len([m for m in manufacturers_in_country if m in major_manufacturers])
        else:
            return 0.6 / len([m for m in manufacturers_in_country if m not in major_manufacturers])
    
    def _get_tariff_rate(self, country):
        """Get USA tariff rate for country"""
        return TRADE_DATA_CONFIG['usa_tariff_rates'].get(country, 
                TRADE_DATA_CONFIG['usa_tariff_rates']['default'])
    
    def _get_steel_price(self, year):
        """Get steel price for year"""
        if year == 2023:
            return np.mean(list(STEEL_PRICE_DATA['monthly_prices_2023'].values()))
        elif year == 2024:
            return np.mean(list(STEEL_PRICE_DATA['monthly_prices_2024'].values()))
        else:
            return STEEL_PRICE_DATA['default_price']
    
    def _get_ev_share(self, manufacturer, vehicle_type):
        """Get EV share for manufacturer"""
        base_ev_share = MANUFACTURER_MAPPING['manufacturer_ev_share'].get(manufacturer, 0.15)
        
        # Adjust based on vehicle type
        if vehicle_type == 'Commercial':
            return base_ev_share * 0.3  # Commercial EVs are less common
        else:
            return base_ev_share
    
    def _estimate_import_volume(self, country, manufacturer, production_volume):
        """Estimate import volume to USA"""
        # Countries that export significantly to USA
        major_exporters = ['Japan', 'Germany', 'South Korea', 'Mexico', 'Canada']
        
        if country in major_exporters:
            # Export 20-40% of production to USA
            export_rate = random.uniform(0.2, 0.4)
        elif country == 'China':
            # Lower due to tariffs
            export_rate = random.uniform(0.05, 0.15)
        else:
            # Other countries export less
            export_rate = random.uniform(0.05, 0.20)
        
        return int(production_volume * export_rate)
    
    def _enrich_with_estimates(self):
        """Add estimated data where missing"""
        # This method can be expanded to add more sophisticated estimates
        pass
    
    def save_to_excel(self, df, filename=None):
        """Save unified data to Excel with proper formatting"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = UNIFIED_EXCEL_CONFIG['filename_template'].format(timestamp=timestamp)
        
        filepath = os.path.join(EXCEL_DIR, filename)
        
        # Create Excel with formatting
        with pd.ExcelWriter(filepath, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Automotive Data', index=False)
            
            # Get workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['Automotive Data']
            
            # Create formats
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4472C4',
                'font_color': 'white',
                'border': 1,
                'align': 'center'
            })
            
            number_format = workbook.add_format({
                'num_format': '#,##0',
                'border': 1,
                'align': 'right'
            })
            
            percentage_format = workbook.add_format({
                'num_format': '0.0%',
                'border': 1,
                'align': 'right'
            })
            
            currency_format = workbook.add_format({
                'num_format': '$#,##0',
                'border': 1,
                'align': 'right'
            })
            
            text_format = workbook.add_format({
                'border': 1,
                'align': 'center'
            })
            
            # Apply header formatting
            for col_num, column in enumerate(df.columns):
                worksheet.write(0, col_num, column, header_format)
            
            # Apply data formatting
            for row_num in range(1, len(df) + 1):
                for col_num, column in enumerate(df.columns):
                    value = df.iloc[row_num - 1, col_num]
                    
                    if column in ['TariffRate_USA', 'EV_Share']:
                        worksheet.write(row_num, col_num, value, percentage_format)
                    elif column in ['ProductionVolume', 'ImportVolume']:
                        worksheet.write(row_num, col_num, value, number_format)
                    elif column == 'SteelPrice':
                        worksheet.write(row_num, col_num, value, currency_format)
                    else:
                        worksheet.write(row_num, col_num, value, text_format)
            
            # Auto-adjust column widths
            for col_num, column in enumerate(df.columns):
                max_length = max(
                    len(str(column)),
                    df[column].astype(str).str.len().max()
                )
                worksheet.set_column(col_num, col_num, min(max_length + 2, 20))
        
        self.logger.info(f"Unified Excel file saved: {filepath}")
        return filepath
    
    def get_summary_stats(self, df):
        """Get summary statistics of the unified dataset"""
        summary = {
            'total_records': len(df),
            'years_covered': sorted(df['Year'].unique().tolist()),
            'countries_covered': len(df['Country'].unique()),
            'manufacturers_covered': len(df['Manufacturer'].unique()),
            'vehicle_types': df['VehicleType'].unique().tolist(),
            'total_production': df['ProductionVolume'].sum(),
            'total_imports': df['ImportVolume'].sum(),
            'avg_ev_share': df['EV_Share'].mean(),
            'avg_steel_price': df['SteelPrice'].mean(),
            'top_producers': df.groupby('Manufacturer')['ProductionVolume'].sum().nlargest(5).to_dict()
        }
        
        return summary
