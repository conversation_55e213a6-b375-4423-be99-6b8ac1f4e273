"""
Unified Excel Generator
Consolidates all automotive data into a single Excel file with the requested format
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging
import os
from typing import Dict, List, Optional
import xlsxwriter
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

class UnifiedExcelGenerator:
    """Generates unified Excel report with all automotive data"""
    
    def __init__(self, output_dir: str = "output/excel"):
        self.logger = logging.getLogger(__name__)
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Target columns as requested
        self.target_columns = [
            'Year', 'Country', 'Manufacturer', 'VehicleType', 
            'ProductionVolume', 'TariffRate_USA', 'SteelPrice', 
            'EV_Share', 'ImportVolume'
        ]
    
    def create_unified_excel(self, all_data: Dict) -> str:
        """
        Create unified Excel file with all automotive data
        
        Args:
            all_data: Dictionary containing data from all scrapers
            
        Returns:
            Path to generated Excel file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"automotive_data_unified_{timestamp}.xlsx"
        filepath = os.path.join(self.output_dir, filename)
        
        self.logger.info(f"Creating unified Excel file: {filename}")
        
        try:
            # Create consolidated dataframe
            consolidated_df = self._consolidate_all_data(all_data)
            
            # Create Excel file with multiple sheets
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # Main consolidated sheet
                consolidated_df.to_excel(writer, sheet_name='Consolidated_Data', index=False)
                
                # Individual data sheets
                if 'oica_data' in all_data and all_data['oica_data']:
                    oica_df = pd.DataFrame(all_data['oica_data'])
                    oica_df.to_excel(writer, sheet_name='OICA_Production', index=False)
                
                if 'trade_data' in all_data and all_data['trade_data']:
                    trade_df = pd.DataFrame(all_data['trade_data'])
                    trade_df.to_excel(writer, sheet_name='Trade_Data', index=False)
                
                if 'materials_data' in all_data and all_data['materials_data']:
                    materials_df = pd.DataFrame(all_data['materials_data'])
                    materials_df.to_excel(writer, sheet_name='Materials_Prices', index=False)
                
                if 'automaker_data' in all_data and all_data['automaker_data']:
                    automaker_df = pd.DataFrame(all_data['automaker_data'])
                    automaker_df.to_excel(writer, sheet_name='Automaker_Data', index=False)
                
                if 'ev_data' in all_data and all_data['ev_data']:
                    ev_df = pd.DataFrame(all_data['ev_data'])
                    ev_df.to_excel(writer, sheet_name='EV_Data', index=False)
                
                # Summary sheet
                summary_df = self._create_summary_data(consolidated_df)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
            # Apply formatting
            self._format_excel_file(filepath)
            
            self.logger.info(f"Unified Excel file created successfully: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Failed to create unified Excel file: {e}")
            raise
    
    def _consolidate_all_data(self, all_data: Dict) -> pd.DataFrame:
        """Consolidate all data sources into single dataframe with target format"""
        
        consolidated_records = []
        
        # Process OICA production data
        if 'oica_data' in all_data and all_data['oica_data']:
            for record in all_data['oica_data']:
                consolidated_records.append({
                    'Year': record.get('Year'),
                    'Country': record.get('Country'),
                    'Manufacturer': record.get('Manufacturer', 'Various'),
                    'VehicleType': record.get('VehicleType', 'Total'),
                    'ProductionVolume': record.get('ProductionVolume', 0),
                    'TariffRate_USA': None,  # Will be filled from trade data
                    'SteelPrice': None,  # Will be filled from materials data
                    'EV_Share': None,  # Will be filled from EV data
                    'ImportVolume': None  # Will be filled from trade data
                })
        
        # Create base dataframe
        if consolidated_records:
            df = pd.DataFrame(consolidated_records)
        else:
            # Create empty dataframe with target structure
            df = pd.DataFrame(columns=self.target_columns)
        
        # Fill in trade data
        if 'trade_data' in all_data and all_data['trade_data']:
            df = self._merge_trade_data(df, all_data['trade_data'])
        
        # Fill in materials data
        if 'materials_data' in all_data and all_data['materials_data']:
            df = self._merge_materials_data(df, all_data['materials_data'])
        
        # Fill in EV data
        if 'ev_data' in all_data and all_data['ev_data']:
            df = self._merge_ev_data(df, all_data['ev_data'])
        
        # Fill in automaker data
        if 'automaker_data' in all_data and all_data['automaker_data']:
            df = self._merge_automaker_data(df, all_data['automaker_data'])
        
        # Ensure all target columns exist
        for col in self.target_columns:
            if col not in df.columns:
                df[col] = None
        
        # Reorder columns to match target format
        df = df[self.target_columns]
        
        # Sort by Year, Country, Manufacturer
        df = df.sort_values(['Year', 'Country', 'Manufacturer'], na_position='last')
        
        return df
    
    def _merge_trade_data(self, df: pd.DataFrame, trade_data: List[Dict]) -> pd.DataFrame:
        """Merge trade data into consolidated dataframe"""
        
        trade_df = pd.DataFrame(trade_data)
        
        if not trade_df.empty:
            # Create mapping for tariff rates and import volumes
            for _, trade_row in trade_df.iterrows():
                year = trade_row.get('Year')
                country = trade_row.get('Country')
                
                # Update matching rows
                mask = (df['Year'] == year) & (df['Country'] == country)
                if mask.any():
                    df.loc[mask, 'TariffRate_USA'] = trade_row.get('TariffRate_USA')
                    df.loc[mask, 'ImportVolume'] = trade_row.get('ImportVolume')
        
        return df
    
    def _merge_materials_data(self, df: pd.DataFrame, materials_data: List[Dict]) -> pd.DataFrame:
        """Merge materials pricing data into consolidated dataframe"""
        
        materials_df = pd.DataFrame(materials_data)
        
        if not materials_df.empty:
            # Create mapping for steel prices by year
            for _, materials_row in materials_df.iterrows():
                year = materials_row.get('Year')
                steel_price = materials_row.get('SteelPrice')
                
                # Update all rows for this year
                mask = df['Year'] == year
                if mask.any():
                    df.loc[mask, 'SteelPrice'] = steel_price
        
        return df
    
    def _merge_ev_data(self, df: pd.DataFrame, ev_data: List[Dict]) -> pd.DataFrame:
        """Merge EV data into consolidated dataframe"""
        
        ev_df = pd.DataFrame(ev_data)
        
        if not ev_df.empty:
            # Create mapping for EV shares by year and country
            for _, ev_row in ev_df.iterrows():
                year = ev_row.get('Year')
                country = ev_row.get('Country')
                ev_share = ev_row.get('EV_Share')
                
                # Update matching rows
                if year and country and ev_share is not None:
                    mask = (df['Year'] == year) & (df['Country'] == country)
                    if mask.any():
                        df.loc[mask, 'EV_Share'] = f"{ev_share}%"
        
        return df
    
    def _merge_automaker_data(self, df: pd.DataFrame, automaker_data: List[Dict]) -> pd.DataFrame:
        """Merge automaker data into consolidated dataframe"""
        
        automaker_df = pd.DataFrame(automaker_data)
        
        if not automaker_df.empty:
            # Add automaker-specific records
            for _, automaker_row in automaker_df.iterrows():
                # Check if this manufacturer/year/country combination already exists
                year = automaker_row.get('Year')
                country = automaker_row.get('Country')
                manufacturer = automaker_row.get('Manufacturer')
                
                mask = (df['Year'] == year) & (df['Country'] == country) & (df['Manufacturer'] == manufacturer)
                
                if not mask.any():
                    # Add new record for this manufacturer
                    new_record = {
                        'Year': year,
                        'Country': country,
                        'Manufacturer': manufacturer,
                        'VehicleType': automaker_row.get('VehicleType', 'Total'),
                        'ProductionVolume': automaker_row.get('ProductionVolume', 0),
                        'TariffRate_USA': None,
                        'SteelPrice': None,
                        'EV_Share': f"{automaker_row.get('EV_Share', 0)}%" if automaker_row.get('EV_Share') else None,
                        'ImportVolume': None
                    }
                    df = pd.concat([df, pd.DataFrame([new_record])], ignore_index=True)
        
        return df
    
    def _create_summary_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create summary statistics"""
        
        summary_data = []
        
        if not df.empty:
            # Summary by year
            yearly_summary = df.groupby('Year').agg({
                'ProductionVolume': ['sum', 'mean', 'count'],
                'ImportVolume': ['sum', 'mean'],
                'SteelPrice': 'mean'
            }).round(2)
            
            yearly_summary.columns = ['Total_Production', 'Avg_Production', 'Country_Count', 
                                    'Total_Imports', 'Avg_Imports', 'Avg_Steel_Price']
            yearly_summary = yearly_summary.reset_index()
            
            # Summary by country
            country_summary = df.groupby('Country').agg({
                'ProductionVolume': ['sum', 'mean'],
                'ImportVolume': ['sum', 'mean']
            }).round(2)
            
            country_summary.columns = ['Total_Production', 'Avg_Production', 'Total_Imports', 'Avg_Imports']
            country_summary = country_summary.reset_index()
            
            # Combine summaries
            summary_data = [
                {'Summary_Type': 'By Year', 'Data': yearly_summary.to_dict('records')},
                {'Summary_Type': 'By Country', 'Data': country_summary.to_dict('records')}
            ]
        
        return pd.DataFrame(summary_data)
    
    def _format_excel_file(self, filepath: str):
        """Apply formatting to Excel file"""
        
        try:
            from openpyxl import load_workbook
            from openpyxl.styles import Font, PatternFill, Alignment
            
            wb = load_workbook(filepath)
            
            # Format main sheet
            if 'Consolidated_Data' in wb.sheetnames:
                ws = wb['Consolidated_Data']
                
                # Header formatting
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                
                for cell in ws[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal="center")
                
                # Auto-adjust column widths
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width
            
            wb.save(filepath)
            
        except Exception as e:
            self.logger.warning(f"Failed to apply Excel formatting: {e}")
    
    def create_sample_data(self, years: List[int]) -> str:
        """Create sample data file for testing"""
        
        sample_data = []
        countries = ['Japan', 'Germany', 'USA', 'China', 'South Korea']
        manufacturers = ['Toyota', 'Volkswagen', 'General Motors', 'BYD', 'Hyundai']
        
        for year in years:
            for i, country in enumerate(countries):
                sample_data.append({
                    'Year': year,
                    'Country': country,
                    'Manufacturer': manufacturers[i],
                    'VehicleType': 'Passenger',
                    'ProductionVolume': np.random.randint(500000, 10000000),
                    'TariffRate_USA': f"{np.random.uniform(0, 25):.1f}%",
                    'SteelPrice': np.random.randint(500, 1200),
                    'EV_Share': f"{np.random.uniform(1, 30):.1f}%",
                    'ImportVolume': np.random.randint(10000, 2000000)
                })
        
        # Create Excel file
        all_data = {'sample_data': sample_data}
        return self.create_unified_excel(all_data)
