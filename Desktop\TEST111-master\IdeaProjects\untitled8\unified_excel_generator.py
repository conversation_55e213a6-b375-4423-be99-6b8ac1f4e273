"""
Unified Excel Generator
Consolidates all automotive data into a single Excel file with the requested format
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging
import os
from typing import Dict, List, Optional
import xlsxwriter
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

class UnifiedExcelGenerator:
    """Generates unified Excel report with all automotive data"""
    
    def __init__(self, output_dir: str = "output/excel"):
        self.logger = logging.getLogger(__name__)
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Target columns as requested
        self.target_columns = [
            'Year', 'Country', 'Manufacturer', 'VehicleType', 
            'ProductionVolume', 'TariffRate_USA', 'SteelPrice', 
            'EV_Share', 'ImportVolume'
        ]
    
    def create_unified_excel(self, all_data: Dict) -> str:
        """
        Create unified Excel file with single sheet containing all automotive data

        Args:
            all_data: Dictionary containing data from all scrapers

        Returns:
            Path to generated Excel file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"automotive_data_single_sheet_{timestamp}.xlsx"
        filepath = os.path.join(self.output_dir, filename)

        self.logger.info(f"Creating single-sheet Excel file: {filename}")

        try:
            # Create consolidated dataframe
            consolidated_df = self._consolidate_all_data(all_data)

            # Clean and format the data for single sheet
            final_df = self._prepare_single_sheet_data(consolidated_df)

            # Create Excel file with single sheet
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                final_df.to_excel(writer, sheet_name='Automotive_Data', index=False)

            # Apply formatting
            self._format_single_sheet_excel(filepath)

            self.logger.info(f"Single-sheet Excel file created successfully: {filepath}")
            return filepath

        except Exception as e:
            self.logger.error(f"Failed to create single-sheet Excel file: {e}")
            raise
    
    def _consolidate_all_data(self, all_data: Dict) -> pd.DataFrame:
        """Consolidate all data sources into single dataframe with target format"""
        
        consolidated_records = []
        
        # Process OICA production data
        if 'oica_data' in all_data and all_data['oica_data']:
            for record in all_data['oica_data']:
                consolidated_records.append({
                    'Year': record.get('Year'),
                    'Country': record.get('Country'),
                    'Manufacturer': record.get('Manufacturer', 'Various'),
                    'VehicleType': record.get('VehicleType', 'Total'),
                    'ProductionVolume': record.get('ProductionVolume', 0),
                    'TariffRate_USA': None,  # Will be filled from trade data
                    'SteelPrice': None,  # Will be filled from materials data
                    'EV_Share': None,  # Will be filled from EV data
                    'ImportVolume': None  # Will be filled from trade data
                })
        
        # Create base dataframe
        if consolidated_records:
            df = pd.DataFrame(consolidated_records)
        else:
            # Create empty dataframe with target structure
            df = pd.DataFrame(columns=self.target_columns)
        
        # Fill in trade data
        if 'trade_data' in all_data and all_data['trade_data']:
            df = self._merge_trade_data(df, all_data['trade_data'])
        
        # Fill in materials data
        if 'materials_data' in all_data and all_data['materials_data']:
            df = self._merge_materials_data(df, all_data['materials_data'])
        
        # Fill in EV data
        if 'ev_data' in all_data and all_data['ev_data']:
            df = self._merge_ev_data(df, all_data['ev_data'])
        
        # Fill in automaker data
        if 'automaker_data' in all_data and all_data['automaker_data']:
            df = self._merge_automaker_data(df, all_data['automaker_data'])
        
        # Ensure all target columns exist
        for col in self.target_columns:
            if col not in df.columns:
                df[col] = None
        
        # Reorder columns to match target format
        df = df[self.target_columns]
        
        # Sort by Year, Country, Manufacturer
        df = df.sort_values(['Year', 'Country', 'Manufacturer'], na_position='last')
        
        return df
    
    def _merge_trade_data(self, df: pd.DataFrame, trade_data: List[Dict]) -> pd.DataFrame:
        """Merge trade data into consolidated dataframe"""
        
        trade_df = pd.DataFrame(trade_data)
        
        if not trade_df.empty:
            # Create mapping for tariff rates and import volumes
            for _, trade_row in trade_df.iterrows():
                year = trade_row.get('Year')
                country = trade_row.get('Country')
                
                # Update matching rows
                mask = (df['Year'] == year) & (df['Country'] == country)
                if mask.any():
                    df.loc[mask, 'TariffRate_USA'] = trade_row.get('TariffRate_USA')
                    df.loc[mask, 'ImportVolume'] = trade_row.get('ImportVolume')
        
        return df
    
    def _merge_materials_data(self, df: pd.DataFrame, materials_data: List[Dict]) -> pd.DataFrame:
        """Merge materials pricing data into consolidated dataframe"""
        
        materials_df = pd.DataFrame(materials_data)
        
        if not materials_df.empty:
            # Create mapping for steel prices by year
            for _, materials_row in materials_df.iterrows():
                year = materials_row.get('Year')
                steel_price = materials_row.get('SteelPrice')
                
                # Update all rows for this year
                mask = df['Year'] == year
                if mask.any():
                    df.loc[mask, 'SteelPrice'] = steel_price
        
        return df
    
    def _merge_ev_data(self, df: pd.DataFrame, ev_data: List[Dict]) -> pd.DataFrame:
        """Merge EV data into consolidated dataframe"""
        
        ev_df = pd.DataFrame(ev_data)
        
        if not ev_df.empty:
            # Create mapping for EV shares by year and country
            for _, ev_row in ev_df.iterrows():
                year = ev_row.get('Year')
                country = ev_row.get('Country')
                ev_share = ev_row.get('EV_Share')
                
                # Update matching rows
                if year and country and ev_share is not None:
                    mask = (df['Year'] == year) & (df['Country'] == country)
                    if mask.any():
                        df.loc[mask, 'EV_Share'] = f"{ev_share}%"
        
        return df
    
    def _merge_automaker_data(self, df: pd.DataFrame, automaker_data: List[Dict]) -> pd.DataFrame:
        """Merge automaker data into consolidated dataframe"""
        
        automaker_df = pd.DataFrame(automaker_data)
        
        if not automaker_df.empty:
            # Add automaker-specific records
            for _, automaker_row in automaker_df.iterrows():
                # Check if this manufacturer/year/country combination already exists
                year = automaker_row.get('Year')
                country = automaker_row.get('Country')
                manufacturer = automaker_row.get('Manufacturer')
                
                mask = (df['Year'] == year) & (df['Country'] == country) & (df['Manufacturer'] == manufacturer)
                
                if not mask.any():
                    # Add new record for this manufacturer
                    new_record = {
                        'Year': year,
                        'Country': country,
                        'Manufacturer': manufacturer,
                        'VehicleType': automaker_row.get('VehicleType', 'Total'),
                        'ProductionVolume': automaker_row.get('ProductionVolume', 0),
                        'TariffRate_USA': None,
                        'SteelPrice': None,
                        'EV_Share': f"{automaker_row.get('EV_Share', 0)}%" if automaker_row.get('EV_Share') else None,
                        'ImportVolume': None
                    }
                    df = pd.concat([df, pd.DataFrame([new_record])], ignore_index=True)
        
        return df

    def _prepare_single_sheet_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for single sheet with clean formatting"""

        # Ensure all target columns exist
        for col in self.target_columns:
            if col not in df.columns:
                df[col] = None

        # Select only target columns in correct order
        final_df = df[self.target_columns].copy()

        # Clean and format data
        final_df = final_df.dropna(subset=['Year', 'Country', 'Manufacturer'])  # Remove rows without essential data

        # Format numeric columns
        if 'ProductionVolume' in final_df.columns:
            final_df['ProductionVolume'] = pd.to_numeric(final_df['ProductionVolume'], errors='coerce').fillna(0).astype(int)

        if 'ImportVolume' in final_df.columns:
            final_df['ImportVolume'] = pd.to_numeric(final_df['ImportVolume'], errors='coerce').fillna(0).astype(int)

        if 'SteelPrice' in final_df.columns:
            final_df['SteelPrice'] = pd.to_numeric(final_df['SteelPrice'], errors='coerce').fillna(0).astype(int)

        # Format percentage columns
        for col in ['TariffRate_USA', 'EV_Share']:
            if col in final_df.columns:
                final_df[col] = final_df[col].apply(self._format_percentage)

        # Fill missing values with appropriate defaults
        final_df['VehicleType'] = final_df['VehicleType'].fillna('Passenger')
        final_df['TariffRate_USA'] = final_df['TariffRate_USA'].fillna('N/A')
        final_df['EV_Share'] = final_df['EV_Share'].fillna('N/A')
        final_df['SteelPrice'] = final_df['SteelPrice'].fillna(0)
        final_df['ImportVolume'] = final_df['ImportVolume'].fillna(0)

        # Sort by Year, Country, Manufacturer
        final_df = final_df.sort_values(['Year', 'Country', 'Manufacturer'])

        # Reset index
        final_df = final_df.reset_index(drop=True)

        return final_df

    def _format_percentage(self, value):
        """Format percentage values consistently"""
        if pd.isna(value) or value is None:
            return 'N/A'

        if isinstance(value, str):
            if '%' in value:
                return value
            try:
                num_val = float(value)
                return f"{num_val}%"
            except:
                return 'N/A'

        if isinstance(value, (int, float)):
            return f"{value}%"

        return 'N/A'

    def _create_summary_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create summary statistics"""
        
        summary_data = []
        
        if not df.empty:
            # Summary by year
            yearly_summary = df.groupby('Year').agg({
                'ProductionVolume': ['sum', 'mean', 'count'],
                'ImportVolume': ['sum', 'mean'],
                'SteelPrice': 'mean'
            }).round(2)
            
            yearly_summary.columns = ['Total_Production', 'Avg_Production', 'Country_Count', 
                                    'Total_Imports', 'Avg_Imports', 'Avg_Steel_Price']
            yearly_summary = yearly_summary.reset_index()
            
            # Summary by country
            country_summary = df.groupby('Country').agg({
                'ProductionVolume': ['sum', 'mean'],
                'ImportVolume': ['sum', 'mean']
            }).round(2)
            
            country_summary.columns = ['Total_Production', 'Avg_Production', 'Total_Imports', 'Avg_Imports']
            country_summary = country_summary.reset_index()
            
            # Combine summaries
            summary_data = [
                {'Summary_Type': 'By Year', 'Data': yearly_summary.to_dict('records')},
                {'Summary_Type': 'By Country', 'Data': country_summary.to_dict('records')}
            ]
        
        return pd.DataFrame(summary_data)

    def _format_single_sheet_excel(self, filepath: str):
        """Apply formatting to single sheet Excel file"""

        try:
            from openpyxl import load_workbook
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            wb = load_workbook(filepath)
            ws = wb['Automotive_Data']

            # Header formatting
            header_font = Font(bold=True, color="FFFFFF", size=12)
            header_fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")

            # Apply header formatting
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment

            # Data formatting
            data_font = Font(size=10)
            data_alignment = Alignment(horizontal="left", vertical="center")

            # Apply data formatting
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.font = data_font
                    cell.alignment = data_alignment

            # Number formatting for specific columns
            for row in ws.iter_rows(min_row=2):
                # ProductionVolume (column E)
                if len(row) > 4 and row[4].value:
                    row[4].number_format = '#,##0'

                # SteelPrice (column G)
                if len(row) > 6 and row[6].value:
                    row[6].number_format = '#,##0'

                # ImportVolume (column I)
                if len(row) > 8 and row[8].value:
                    row[8].number_format = '#,##0'

            # Auto-adjust column widths
            column_widths = {
                'A': 8,   # Year
                'B': 15,  # Country
                'C': 18,  # Manufacturer
                'D': 12,  # VehicleType
                'E': 18,  # ProductionVolume
                'F': 15,  # TariffRate_USA
                'G': 12,  # SteelPrice
                'H': 12,  # EV_Share
                'I': 15   # ImportVolume
            }

            for col, width in column_widths.items():
                ws.column_dimensions[col].width = width

            # Add borders
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            for row in ws.iter_rows():
                for cell in row:
                    cell.border = thin_border

            # Freeze header row
            ws.freeze_panes = 'A2'

            wb.save(filepath)

        except Exception as e:
            self.logger.warning(f"Failed to apply Excel formatting: {e}")

    def _format_excel_file(self, filepath: str):
        """Apply formatting to Excel file"""
        
        try:
            from openpyxl import load_workbook
            from openpyxl.styles import Font, PatternFill, Alignment
            
            wb = load_workbook(filepath)
            
            # Format main sheet
            if 'Consolidated_Data' in wb.sheetnames:
                ws = wb['Consolidated_Data']
                
                # Header formatting
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                
                for cell in ws[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal="center")
                
                # Auto-adjust column widths
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width
            
            wb.save(filepath)
            
        except Exception as e:
            self.logger.warning(f"Failed to apply Excel formatting: {e}")
    
    def create_sample_data(self, years: List[int]) -> str:
        """Create sample data file for testing"""
        
        sample_data = []
        countries = ['Japan', 'Germany', 'USA', 'China', 'South Korea']
        manufacturers = ['Toyota', 'Volkswagen', 'General Motors', 'BYD', 'Hyundai']
        
        for year in years:
            for i, country in enumerate(countries):
                sample_data.append({
                    'Year': year,
                    'Country': country,
                    'Manufacturer': manufacturers[i],
                    'VehicleType': 'Passenger',
                    'ProductionVolume': np.random.randint(500000, 10000000),
                    'TariffRate_USA': f"{np.random.uniform(0, 25):.1f}%",
                    'SteelPrice': np.random.randint(500, 1200),
                    'EV_Share': f"{np.random.uniform(1, 30):.1f}%",
                    'ImportVolume': np.random.randint(10000, 2000000)
                })
        
        # Create Excel file
        all_data = {'sample_data': sample_data}
        return self.create_unified_excel(all_data)
