"""
Configuration file for OICA scraper
"""

# Base URLs
BASE_URL = "https://www.oica.net"
PRODUCTION_STATS_BASE = "https://www.oica.net/category/production-statistics"

# Available years for scraping
AVAILABLE_YEARS = [
    2024, 2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015,
    2014, 2013, 2012, 2011, 2010, 2009, 2008, 2007, 2006, 2005,
    2004, 2003, 2002, 2001, 2000, 1999
]

# Default year
DEFAULT_YEAR = 2019

# Output directories
OUTPUT_DIR = "output"
DATA_DIR = f"{OUTPUT_DIR}/data"
FILES_DIR = f"{OUTPUT_DIR}/files"

# Request settings
REQUEST_TIMEOUT = 30
REQUEST_DELAY = 1  # seconds between requests
MAX_RETRIES = 3

# Headers for requests
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# File types to download
DOWNLOAD_EXTENSIONS = ['.pdf', '.xlsx', '.xls']

# Table column mappings
TABLE_COLUMNS = {
    'Country/Region': 'country_region',
    'Cars': 'cars',
    'Commercial vehicles': 'commercial_vehicles', 
    'Total': 'total',
    '% change': 'percent_change'
}
