"""
Materials Price Scraper
Scrapes raw material pricing data from various sources including steel, aluminum, and other automotive materials
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import logging
import re
from typing import Dict, List, Optional
import yfinance as yf
from datetime import datetime, timedelta

class MaterialsScraper:
    """Scraper for raw materials pricing data"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Material symbols for Yahoo Finance
        self.material_symbols = {
            'steel': 'MT',  # ArcelorMittal as steel proxy
            'aluminum': 'AA',  # Alcoa as aluminum proxy
            'copper': 'HG=F',  # Copper futures
            'iron_ore': 'VALE',  # Vale as iron ore proxy
        }
    
    def get_materials_data(self, years: List[int]) -> List[Dict]:
        """
        Get materials pricing data for specified years
        
        Args:
            years: List of years to get data for
            
        Returns:
            List of dictionaries containing materials pricing data
        """
        all_data = []
        
        for year in years:
            self.logger.info(f"Getting materials data for year {year}")
            try:
                # Get steel prices
                steel_price = self._get_steel_price(year)
                
                # Get aluminum prices
                aluminum_price = self._get_aluminum_price(year)
                
                # Get other material prices
                other_materials = self._get_other_materials_prices(year)
                
                # Combine data
                year_data = {
                    'Year': year,
                    'SteelPrice': steel_price,
                    'AluminumPrice': aluminum_price,
                    'CopperPrice': other_materials.get('copper', 0),
                    'IronOrePrice': other_materials.get('iron_ore', 0),
                    'Source': 'Multiple'
                }
                
                all_data.append(year_data)
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                self.logger.error(f"Failed to get materials data for year {year}: {e}")
                
        return all_data
    
    def _get_steel_price(self, year: int) -> float:
        """Get average steel price for a given year"""
        try:
            # Try to get data from Yahoo Finance using steel company as proxy
            ticker = yf.Ticker(self.material_symbols['steel'])
            
            # Get historical data for the year
            start_date = f"{year}-01-01"
            end_date = f"{year}-12-31"
            
            hist = ticker.history(start=start_date, end=end_date)
            
            if not hist.empty:
                # Use average closing price as proxy for steel price
                avg_price = hist['Close'].mean()
                # Convert to approximate steel price per ton (simplified calculation)
                steel_price = avg_price * 10  # Rough conversion factor
                return round(steel_price, 2)
            
        except Exception as e:
            self.logger.warning(f"Failed to get steel price from Yahoo Finance: {e}")
        
        # Fallback to estimated steel prices
        return self._get_estimated_steel_price(year)
    
    def _get_estimated_steel_price(self, year: int) -> float:
        """Get estimated steel price based on historical trends"""
        
        # Historical steel price estimates (USD per metric ton)
        steel_prices = {
            2014: 550,
            2015: 450,
            2016: 500,
            2017: 600,
            2018: 750,
            2019: 650,
            2020: 550,
            2021: 1200,  # Post-COVID surge
            2022: 900,
            2023: 720,
            2024: 680,
        }
        
        return steel_prices.get(year, 650)  # Default price
    
    def _get_aluminum_price(self, year: int) -> float:
        """Get average aluminum price for a given year"""
        try:
            # Try to get data from Yahoo Finance
            ticker = yf.Ticker(self.material_symbols['aluminum'])
            
            start_date = f"{year}-01-01"
            end_date = f"{year}-12-31"
            
            hist = ticker.history(start=start_date, end=end_date)
            
            if not hist.empty:
                avg_price = hist['Close'].mean()
                # Convert to approximate aluminum price per ton
                aluminum_price = avg_price * 50  # Rough conversion factor
                return round(aluminum_price, 2)
            
        except Exception as e:
            self.logger.warning(f"Failed to get aluminum price from Yahoo Finance: {e}")
        
        # Fallback to estimated aluminum prices
        return self._get_estimated_aluminum_price(year)
    
    def _get_estimated_aluminum_price(self, year: int) -> float:
        """Get estimated aluminum price based on historical trends"""
        
        # Historical aluminum price estimates (USD per metric ton)
        aluminum_prices = {
            2014: 1800,
            2015: 1600,
            2016: 1550,
            2017: 1900,
            2018: 2100,
            2019: 1800,
            2020: 1700,
            2021: 2500,
            2022: 2400,
            2023: 2200,
            2024: 2100,
        }
        
        return aluminum_prices.get(year, 2000)  # Default price
    
    def _get_other_materials_prices(self, year: int) -> Dict[str, float]:
        """Get prices for other materials like copper and iron ore"""
        materials_data = {}
        
        # Copper prices
        try:
            copper_ticker = yf.Ticker(self.material_symbols['copper'])
            start_date = f"{year}-01-01"
            end_date = f"{year}-12-31"
            
            copper_hist = copper_ticker.history(start=start_date, end=end_date)
            if not copper_hist.empty:
                materials_data['copper'] = round(copper_hist['Close'].mean() * 2204.62, 2)  # Convert to USD/ton
            else:
                materials_data['copper'] = self._get_estimated_copper_price(year)
                
        except Exception as e:
            self.logger.warning(f"Failed to get copper price: {e}")
            materials_data['copper'] = self._get_estimated_copper_price(year)
        
        # Iron ore prices (using Vale as proxy)
        try:
            iron_ticker = yf.Ticker(self.material_symbols['iron_ore'])
            iron_hist = iron_ticker.history(start=start_date, end=end_date)
            if not iron_hist.empty:
                materials_data['iron_ore'] = round(iron_hist['Close'].mean() * 5, 2)  # Rough conversion
            else:
                materials_data['iron_ore'] = self._get_estimated_iron_ore_price(year)
                
        except Exception as e:
            self.logger.warning(f"Failed to get iron ore price: {e}")
            materials_data['iron_ore'] = self._get_estimated_iron_ore_price(year)
        
        return materials_data
    
    def _get_estimated_copper_price(self, year: int) -> float:
        """Get estimated copper price"""
        copper_prices = {
            2014: 6800,
            2015: 5500,
            2016: 4900,
            2017: 6200,
            2018: 6500,
            2019: 6000,
            2020: 6200,
            2021: 9500,
            2022: 8000,
            2023: 8500,
            2024: 8200,
        }
        return copper_prices.get(year, 7000)
    
    def _get_estimated_iron_ore_price(self, year: int) -> float:
        """Get estimated iron ore price"""
        iron_ore_prices = {
            2014: 95,
            2015: 55,
            2016: 60,
            2017: 70,
            2018: 70,
            2019: 90,
            2020: 110,
            2021: 160,
            2022: 120,
            2023: 110,
            2024: 100,
        }
        return iron_ore_prices.get(year, 90)
    
    def get_lme_data(self, years: List[int]) -> List[Dict]:
        """Get London Metal Exchange data (placeholder)"""
        # This would scrape from LME website
        # For now, return empty list as it requires special access
        self.logger.info("LME data scraping not implemented - requires API access")
        return []
    
    def get_fred_data(self, years: List[int]) -> List[Dict]:
        """Get FRED (Federal Reserve Economic Data) materials data"""
        # This would use FRED API
        # For now, return empty list
        self.logger.info("FRED data scraping not implemented - requires API key")
        return []
