"""
Raw Materials Pricing Scraper
Scrapes pricing data for automotive-relevant raw materials from multiple sources
"""

import os
import re
import json
import pandas as pd
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging
from datetime import datetime, timedelta
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhanced_config import MATERIALS_CONFIG, REQUEST_CONFIG, DATA_DIR
from utils import make_request, clean_numeric_value, sanitize_filename

class MaterialsScraper:
    """Scraper for raw materials pricing data relevant to automotive industry"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data = []
        self.sources_data = {}
        
    def scrape_all_materials_data(self, days_back=30):
        """Scrape materials pricing data from all configured sources"""
        self.logger.info(f"Scraping materials pricing data for last {days_back} days")
        
        # Scrape from each configured source
        for source_name, source_config in MATERIALS_CONFIG['sources'].items():
            try:
                if source_name == 'lme':
                    self._scrape_lme_data(source_config, days_back)
                elif source_name == 'investing':
                    self._scrape_investing_data(source_config, days_back)
                elif source_name == 'fred':
                    self._scrape_fred_data(source_config, days_back)
                    
            except Exception as e:
                self.logger.error(f"Failed to scrape from {source_name}: {e}")
        
        return self.data
    
    def _scrape_lme_data(self, config, days_back):
        """Scrape London Metal Exchange data"""
        self.logger.info("Scraping LME metals pricing data")
        
        base_url = config['base_url']
        
        for metal in config['metals']:
            try:
                # Construct URL for specific metal
                metal_url = f"{base_url}{config['data_url']}/{metal}"
                response = make_request(metal_url)
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract pricing data
                self._extract_lme_prices(soup, metal, config['automotive_relevance'].get(metal, 'general'))
                
            except Exception as e:
                self.logger.error(f"Error scraping LME data for {metal}: {e}")
    
    def _extract_lme_prices(self, soup, metal, automotive_use):
        """Extract pricing data from LME page"""
        # Look for price tables or data containers
        price_containers = soup.find_all(['table', 'div'], class_=re.compile(r'price|data|quote'))
        
        for container in price_containers:
            # Extract current price
            price_elements = container.find_all(text=re.compile(r'\$[\d,]+\.?\d*'))
            
            for price_text in price_elements:
                try:
                    price = self._parse_price(price_text)
                    if price:
                        self.data.append({
                            'material': metal,
                            'price': price,
                            'currency': 'USD',
                            'unit': 'per_tonne',
                            'date': datetime.now().strftime('%Y-%m-%d'),
                            'source': 'LME',
                            'automotive_use': automotive_use,
                            'price_type': 'spot'
                        })
                        break  # Take first valid price found
                except Exception as e:
                    self.logger.error(f"Error parsing LME price for {metal}: {e}")
    
    def _scrape_investing_data(self, config, days_back):
        """Scrape Investing.com commodities data"""
        self.logger.info("Scraping Investing.com commodities data")
        
        base_url = config['base_url']
        
        for material in config['materials']:
            try:
                # Construct URL for specific commodity
                commodity_url = f"{base_url}{config['commodities_url']}/{material}"
                response = make_request(commodity_url)
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract pricing data
                self._extract_investing_prices(soup, material, config['automotive_relevance'].get(material, 'general'))
                
            except Exception as e:
                self.logger.error(f"Error scraping Investing.com data for {material}: {e}")
    
    def _extract_investing_prices(self, soup, material, automotive_use):
        """Extract pricing data from Investing.com page"""
        # Look for price display elements
        price_selectors = [
            '[data-test="instrument-price-last"]',
            '.text-2xl',
            '.instrument-price_last__JQN7O',
            '.last-price-value'
        ]
        
        for selector in price_selectors:
            price_element = soup.select_one(selector)
            if price_element:
                try:
                    price_text = price_element.get_text(strip=True)
                    price = self._parse_price(price_text)
                    
                    if price:
                        # Determine unit based on material
                        unit = self._get_material_unit(material)
                        
                        self.data.append({
                            'material': material,
                            'price': price,
                            'currency': 'USD',
                            'unit': unit,
                            'date': datetime.now().strftime('%Y-%m-%d'),
                            'source': 'Investing.com',
                            'automotive_use': automotive_use,
                            'price_type': 'current'
                        })
                        break
                        
                except Exception as e:
                    self.logger.error(f"Error parsing Investing.com price for {material}: {e}")
    
    def _scrape_fred_data(self, config, days_back):
        """Scrape Federal Reserve Economic Data (FRED)"""
        self.logger.info("Scraping FRED economic data")
        
        # Note: FRED typically requires API key for full access
        # This is a simplified scraper for publicly available data
        
        base_url = config['base_url']
        
        for series_name, series_id in config['series_ids'].items():
            try:
                # Construct URL for FRED series
                series_url = f"{base_url}/series/{series_id}"
                response = make_request(series_url)
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract latest data point
                self._extract_fred_data(soup, series_name, series_id)
                
            except Exception as e:
                self.logger.error(f"Error scraping FRED data for {series_name}: {e}")
    
    def _extract_fred_data(self, soup, series_name, series_id):
        """Extract data from FRED series page"""
        # Look for the latest observation value
        value_selectors = [
            '.series-meta-observation-value',
            '.observation-value',
            '.latest-observation'
        ]
        
        for selector in value_selectors:
            value_element = soup.select_one(selector)
            if value_element:
                try:
                    value_text = value_element.get_text(strip=True)
                    value = clean_numeric_value(value_text)
                    
                    if value:
                        # Extract date if available
                        date_element = soup.select_one('.series-meta-observation-date, .observation-date')
                        date_str = date_element.get_text(strip=True) if date_element else datetime.now().strftime('%Y-%m-%d')
                        
                        self.data.append({
                            'material': series_name.replace('_price', ''),
                            'price': value,
                            'currency': 'USD',
                            'unit': self._get_fred_unit(series_name),
                            'date': date_str,
                            'source': 'FRED',
                            'series_id': series_id,
                            'automotive_use': self._get_automotive_use(series_name),
                            'price_type': 'index'
                        })
                        break
                        
                except Exception as e:
                    self.logger.error(f"Error parsing FRED data for {series_name}: {e}")
    
    def _parse_price(self, price_text):
        """Parse price from text string"""
        try:
            # Remove currency symbols and commas
            cleaned = re.sub(r'[^\d.]', '', price_text)
            return float(cleaned) if cleaned else None
        except:
            return None
    
    def _get_material_unit(self, material):
        """Get appropriate unit for material"""
        unit_mapping = {
            'crude-oil': 'per_barrel',
            'natural-gas': 'per_mmbtu',
            'rubber': 'per_kg',
            'platinum': 'per_ounce',
            'palladium': 'per_ounce'
        }
        return unit_mapping.get(material, 'per_unit')
    
    def _get_fred_unit(self, series_name):
        """Get unit for FRED series"""
        if 'price' in series_name.lower():
            if 'oil' in series_name.lower():
                return 'per_barrel'
            else:
                return 'index'
        return 'index'
    
    def _get_automotive_use(self, material_name):
        """Get automotive use for material"""
        use_mapping = {
            'steel': 'chassis_body',
            'aluminum': 'body_panels',
            'copper': 'wiring_electronics',
            'oil': 'fuel_plastics',
            'rubber': 'tires',
            'platinum': 'catalytic_converters',
            'palladium': 'catalytic_converters'
        }
        
        for key, use in use_mapping.items():
            if key in material_name.lower():
                return use
        return 'general'
    
    def get_price_trends(self, material=None, days=30):
        """Analyze price trends for materials"""
        if not self.data:
            return {}
        
        df = pd.DataFrame(self.data)
        
        if material:
            df = df[df['material'] == material]
        
        # Convert date column to datetime
        df['date'] = pd.to_datetime(df['date'])
        
        # Calculate trends
        trends = {}
        for mat in df['material'].unique():
            mat_data = df[df['material'] == mat].sort_values('date')
            if len(mat_data) > 1:
                latest_price = mat_data['price'].iloc[-1]
                previous_price = mat_data['price'].iloc[-2] if len(mat_data) > 1 else latest_price
                
                change = ((latest_price - previous_price) / previous_price * 100) if previous_price != 0 else 0
                
                trends[mat] = {
                    'latest_price': latest_price,
                    'previous_price': previous_price,
                    'change_percent': round(change, 2),
                    'trend': 'up' if change > 0 else 'down' if change < 0 else 'stable',
                    'automotive_use': mat_data['automotive_use'].iloc[-1] if 'automotive_use' in mat_data.columns else 'unknown'
                }
        
        return trends
    
    def save_data(self, filename=None):
        """Save materials pricing data to file"""
        if not self.data:
            self.logger.warning("No materials data to save")
            return None
            
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"materials_pricing_{timestamp}.json"
        
        filepath = os.path.join(DATA_DIR, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"Materials data saved to: {filepath}")
        return filepath
    
    def get_summary(self):
        """Get summary of scraped materials data"""
        if not self.data:
            return "No materials data available"
        
        df = pd.DataFrame(self.data)
        
        summary = {
            'source': 'Materials Pricing',
            'total_records': len(df),
            'materials_covered': df['material'].unique().tolist() if 'material' in df.columns else [],
            'sources_used': df['source'].unique().tolist() if 'source' in df.columns else [],
            'date_range': {
                'earliest': df['date'].min() if 'date' in df.columns else None,
                'latest': df['date'].max() if 'date' in df.columns else None
            },
            'automotive_uses': df['automotive_use'].unique().tolist() if 'automotive_use' in df.columns else [],
            'price_trends': self.get_price_trends()
        }
        
        return summary
