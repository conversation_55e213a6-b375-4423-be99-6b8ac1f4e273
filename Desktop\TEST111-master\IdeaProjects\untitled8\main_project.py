"""
Script principal pour la collecte de données du projet d'analyse automobile
Collecte toutes les données nécessaires pour:
- Analyse descriptive (tendances 2010-2024)
- Évaluation d'impact économique (politiques USA)
- Mod<PERSON>les de prévision (jusqu'en 2030)
- Analyse de transition marché (EV/Hybrid)
"""

import argparse
import sys
import logging
from datetime import datetime

# Setup logging
from enhanced_config import LOGGING_CONFIG

logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG['file']),
        logging.StreamHandler(sys.stdout)
    ]
)

from project_scraper import ProjectDataCollector
from project_config import PROJECT_CONFIG, DELIVERABLES_CONFIG

def main():
    """Fonction principale pour la collecte de données du projet"""
    parser = argparse.ArgumentParser(
        description='Collecteur de données pour projet d\'analyse automobile',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🎯 OBJECTIFS DU PROJET:
  • Analyse descriptive: Tendances production 2010-2024
  • Impact économique: Politiques USA (tarifs, IRA, subventions EV)
  • Prévisions: Modèles jusqu'en 2030
  • Transition marché: Évolution vers EV/Hybrid

📊 DONNÉES COLLECTÉES:
  • Production historique OICA (15 ans)
  • Données commerciales USA (tarifs, imports/exports)
  • Prix matières premières (acier, aluminium, lithium, cuivre)
  • Transition EV (ventes, parts de marché, infrastructure)
  • Données financières constructeurs
  • Politiques économiques (IRA, tarifs, subventions)

Examples:
  python main_project.py                    # Collecte complète
  python main_project.py --quick           # Collecte rapide
  python main_project.py --data-only       # Données seulement (pas d'Excel)
  python main_project.py --verbose         # Mode détaillé
        """
    )
    
    # Options de collecte
    parser.add_argument('--quick', action='store_true',
                       help='Collecte rapide avec données d\'exemple')
    
    parser.add_argument('--data-only', action='store_true',
                       help='Collecte données seulement (pas de génération Excel)')
    
    parser.add_argument('--categories', nargs='+', 
                       choices=['production', 'trade', 'materials', 'ev', 'financial', 'policy'],
                       default=['production', 'trade', 'materials', 'ev', 'financial', 'policy'],
                       help='Catégories de données à collecter')
    
    parser.add_argument('--output-dir', type=str,
                       help='Répertoire de sortie personnalisé')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Mode verbose avec logs détaillés')
    
    args = parser.parse_args()
    
    # Setup logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger = logging.getLogger(__name__)
    
    # Afficher les informations du projet
    print_project_info()
    
    logger.info("🚀 Début de la collecte de données pour le projet d'analyse automobile")
    logger.info(f"Période d'analyse: {PROJECT_CONFIG['analysis_period']['start_year']}-{PROJECT_CONFIG['analysis_period']['end_year']}")
    logger.info(f"Horizon de prévision: {PROJECT_CONFIG['analysis_period']['forecast_horizon']}")
    logger.info(f"Catégories sélectionnées: {args.categories}")
    
    try:
        # Initialiser le collecteur
        collector = ProjectDataCollector()
        
        # Collecter les données
        logger.info("📡 Début de la collecte de données...")
        collected_data = collector.collect_all_project_data()
        
        # Sauvegarder les données
        logger.info("💾 Sauvegarde des données...")
        save_results = collector.save_all_data()
        
        # Afficher les résultats
        print_collection_results(save_results)
        
        logger.info("✅ Collecte de données terminée avec succès")
        
        # Générer le rapport de recommandations
        generate_project_recommendations(save_results['summary'])
        
    except KeyboardInterrupt:
        logger.info("❌ Processus interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Erreur dans le processus principal: {e}", exc_info=True)
        sys.exit(1)

def print_project_info():
    """Affiche les informations du projet"""
    print("\n" + "="*80)
    print("🚗 PROJET D'ANALYSE DE L'INDUSTRIE AUTOMOBILE")
    print("="*80)
    
    print("📋 OBJECTIFS:")
    print("  1. Analyse descriptive: Tendances production mondiale 2010-2024")
    print("  2. Impact économique: Politiques USA (tarifs, IRA, subventions EV)")
    print("  3. Modèles prévision: Forecasting jusqu'en 2030")
    print("  4. Transition marché: Évolution vers véhicules électriques")
    
    print("\n🎯 CONSTRUCTEURS ANALYSÉS:")
    manufacturers = PROJECT_CONFIG['key_manufacturers']
    for i, manufacturer in enumerate(manufacturers[:8], 1):
        print(f"  {i}. {manufacturer}")
    if len(manufacturers) > 8:
        print(f"  ... et {len(manufacturers) - 8} autres")
    
    print("\n🌍 RÉGIONS CLÉS:")
    regions = PROJECT_CONFIG['key_regions']
    print("  " + " • ".join(regions[:6]))
    print("  " + " • ".join(regions[6:]))
    
    print("\n📊 DONNÉES COLLECTÉES:")
    print("  • Production historique OICA (2010-2024)")
    print("  • Données commerciales USA (imports/exports/tarifs)")
    print("  • Prix matières premières (acier, aluminium, lithium, cuivre)")
    print("  • Transition EV (ventes, parts marché, infrastructure)")
    print("  • Données financières constructeurs")
    print("  • Politiques économiques (IRA, tarifs, subventions)")
    
    print("\n" + "="*80)

def print_collection_results(save_results):
    """Affiche les résultats de la collecte"""
    summary = save_results['summary']
    
    print("\n" + "="*80)
    print("📊 RÉSULTATS DE LA COLLECTE DE DONNÉES")
    print("="*80)
    
    print(f"📅 Date de collecte: {summary['collection_date']}")
    print(f"📈 Total enregistrements: {summary['total_records']:,}")
    
    print("\n📋 DONNÉES PAR CATÉGORIE:")
    for category, count in summary['data_categories'].items():
        category_name = {
            'production_historical': 'Production historique',
            'trade_data': 'Données commerciales',
            'raw_materials': 'Matières premières',
            'ev_transition': 'Transition EV',
            'manufacturer_financials': 'Données financières',
            'policy_data': 'Politiques économiques'
        }.get(category, category)
        
        print(f"  • {category_name}: {count:,} enregistrements")
    
    print("\n📁 FICHIERS GÉNÉRÉS:")
    print(f"  📄 Données complètes JSON: {save_results['json_file']}")
    
    for excel_file in save_results['excel_files']:
        filename = excel_file.split('\\')[-1] if '\\' in excel_file else excel_file.split('/')[-1]
        print(f"  📊 Excel: {filename}")
    
    print("\n" + "="*80)

def generate_project_recommendations(summary):
    """Génère des recommandations pour le projet"""
    print("\n" + "="*80)
    print("💡 RECOMMANDATIONS POUR VOTRE PROJET")
    print("="*80)
    
    total_records = summary['total_records']
    
    print("🔍 ANALYSE DESCRIPTIVE:")
    if total_records > 1000:
        print("  ✅ Données suffisantes pour analyse robuste des tendances")
        print("  📈 Recommandé: Analyse de régression pour identifier les facteurs clés")
        print("  📊 Recommandé: Visualisations interactives avec Plotly/Dash")
    else:
        print("  ⚠️  Données limitées - compléter avec sources additionnelles")
    
    print("\n💰 ÉVALUATION IMPACT ÉCONOMIQUE:")
    print("  📋 Analyser corrélation entre tarifs et volumes d'importation")
    print("  🔄 Modéliser l'impact IRA sur adoption EV")
    print("  💲 Calculer élasticité-prix pour différents segments")
    
    print("\n🔮 MODÈLES DE PRÉVISION:")
    print("  📈 ARIMA pour séries temporelles de production")
    print("  🤖 XGBoost pour prévisions multi-variables")
    print("  📊 Prophet pour tendances saisonnières")
    print("  🧠 LSTM pour patterns complexes long-terme")
    
    print("\n🔋 ANALYSE TRANSITION EV:")
    print("  📊 Modèle de diffusion technologique (Bass Model)")
    print("  🌍 Analyse comparative par région")
    print("  ⚡ Impact infrastructure sur adoption")
    
    print("\n🛠️  OUTILS RECOMMANDÉS:")
    print("  🐍 Python: Pandas, Scikit-learn, Prophet, XGBoost")
    print("  📊 Visualisation: Plotly, Dash, Tableau")
    print("  📈 Analyse: Statsmodels, TensorFlow/Keras")
    print("  📋 Reporting: Jupyter Notebooks, Power BI")
    
    print("\n📋 PROCHAINES ÉTAPES:")
    print("  1. Nettoyer et valider les données collectées")
    print("  2. Créer dashboard interactif pour exploration")
    print("  3. Développer modèles de prévision baseline")
    print("  4. Analyser scénarios d'impact politique")
    print("  5. Générer rapport final avec recommandations")
    
    print("\n" + "="*80)
    print("🎯 Vos données sont prêtes pour l'analyse !")
    print("   Commencez par explorer les fichiers Excel générés.")
    print("="*80)

if __name__ == "__main__":
    main()
