"""
Main script to run OICA scraper
"""

import argparse
import sys
from oica_scraper import OICAScraper
from utils import create_directories, is_valid_year
from config import DEFAULT_YEAR, AVAILABLE_YEARS


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='OICA Production Statistics Scraper')
    parser.add_argument('--year', type=int, default=DEFAULT_YEAR,
                       help=f'Year to scrape (default: {DEFAULT_YEAR})')
    parser.add_argument('--download-files', action='store_true',
                       help='Download PDF and Excel files')
    parser.add_argument('--format', choices=['json', 'csv', 'both'], default='both',
                       help='Output format (default: both)')
    parser.add_argument('--list-years', action='store_true',
                       help='List available years')
    
    args = parser.parse_args()
    
    # List available years
    if args.list_years:
        print("Available years for scraping:")
        for year in sorted(AVAILABLE_YEARS, reverse=True):
            print(f"  {year}")
        return
    
    # Validate year
    if not is_valid_year(args.year):
        print(f"Error: Year {args.year} is not available.")
        print("Use --list-years to see available years.")
        sys.exit(1)
    
    # Create output directories
    create_directories()
    
    try:
        # Initialize scraper
        print(f"Starting OICA scraper for year {args.year}")
        scraper = OICAScraper(args.year)
        
        # Scrape production data
        data = scraper.scrape_production_table()
        
        if not data:
            print("No data was scraped. Please check the URL and try again.")
            sys.exit(1)
        
        # Save data
        scraper.save_data(format=args.format)
        
        # Download files if requested
        if args.download_files:
            scraper.find_download_links()
            downloaded_files = scraper.download_files()
            print(f"Downloaded {len(downloaded_files)} files")
        
        # Print summary
        summary = scraper.get_summary()
        print("\n" + "="*50)
        print("SCRAPING SUMMARY")
        print("="*50)
        for key, value in summary.items():
            print(f"{key.replace('_', ' ').title()}: {value:,}" if isinstance(value, int) else f"{key.replace('_', ' ').title()}: {value}")
        
        print(f"\nScraping completed successfully for year {args.year}!")
        
    except Exception as e:
        print(f"Error during scraping: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
