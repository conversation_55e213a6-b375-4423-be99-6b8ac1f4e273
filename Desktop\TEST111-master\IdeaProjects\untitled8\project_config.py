"""
Configuration spécialisée pour le projet d'analyse automobile
Collecte de données pour analyse descriptive, impact économique, prévisions et transition marché
"""

from datetime import datetime

# =============================================================================
# Configuration du projet d'analyse
# =============================================================================
PROJECT_CONFIG = {
    'analysis_period': {
        'start_year': 2010,  # 15 ans d'historique
        'end_year': 2024,
        'forecast_horizon': 2030
    },
    'key_manufacturers': [
        'Toyota', 'Volkswagen Group', 'Ford', 'Hyundai-Kia', 'Stellantis',
        'General Motors', 'BMW Group', 'Mercedes-Benz', 'Honda', 'Nissan',
        'BYD', 'Tesla', 'SAIC', 'Geely', 'Great Wall'
    ],
    'key_regions': [
        'China', 'USA', 'Japan', 'Germany', 'India', 'South Korea',
        'Mexico', 'Brazil', 'Spain', 'France', 'United Kingdom', 'Italy'
    ],
    'vehicle_categories': [
        'Passenger Cars', 'Commercial Vehicles', 'Electric Vehicles', 
        'Hybrid Vehicles', 'Luxury Vehicles', 'SUVs', 'Trucks'
    ]
}

# =============================================================================
# Sources de données étendues
# =============================================================================
EXTENDED_DATA_SOURCES = {
    'production_data': {
        'oica': {
            'url': 'https://www.oica.net/category/production-statistics/',
            'years_available': list(range(1999, 2025)),
            'data_types': ['production_by_country', 'production_by_manufacturer']
        },
        'marklines': {
            'url': 'https://www.marklines.com/en/statistics',
            'data_types': ['monthly_production', 'manufacturer_rankings']
        }
    },
    
    'trade_data': {
        'ustr': {
            'url': 'https://ustr.gov/trade-agreements',
            'data_types': ['tariff_rates', 'trade_volumes', 'policy_changes']
        },
        'wto': {
            'url': 'https://www.wto.org/english/res_e/statis_e/statis_e.htm',
            'data_types': ['global_trade_flows', 'automotive_hs_codes']
        },
        'census_gov': {
            'url': 'https://www.census.gov/foreign-trade/statistics/product/',
            'data_types': ['us_imports', 'us_exports', 'trade_balance']
        }
    },
    
    'economic_policies': {
        'ira_data': {
            'url': 'https://www.energy.gov/lpo/inflation-reduction-act',
            'data_types': ['ev_subsidies', 'tax_credits', 'policy_timeline']
        },
        'tariff_data': {
            'url': 'https://www.trade.gov/tariffs',
            'data_types': ['current_tariffs', 'historical_changes', 'exemptions']
        }
    },
    
    'raw_materials': {
        'steel_prices': {
            'url': 'https://www.steelorbis.com/steel-prices/',
            'data_types': ['hot_rolled_coil', 'cold_rolled_coil', 'galvanized']
        },
        'aluminum_prices': {
            'url': 'https://www.lme.com/en/metals/non-ferrous/lme-aluminium',
            'data_types': ['spot_prices', 'futures', 'regional_premiums']
        },
        'lithium_prices': {
            'url': 'https://www.benchmark-minerals.com/lithium-price-tracker/',
            'data_types': ['lithium_carbonate', 'lithium_hydroxide']
        },
        'copper_prices': {
            'url': 'https://www.lme.com/en/metals/non-ferrous/lme-copper',
            'data_types': ['spot_prices', 'futures']
        }
    },
    
    'ev_transition': {
        'ev_sales': {
            'url': 'https://www.ev-volumes.com/',
            'data_types': ['global_ev_sales', 'market_share', 'battery_capacity']
        },
        'charging_infrastructure': {
            'url': 'https://www.iea.org/data-and-statistics/data-tools/global-ev-data-explorer',
            'data_types': ['charging_stations', 'infrastructure_investment']
        }
    },
    
    'manufacturer_financials': {
        'annual_reports': {
            'toyota': 'https://global.toyota/en/ir/library/annual/',
            'volkswagen': 'https://www.volkswagenag.com/en/InvestorRelations/annual-reports.html',
            'ford': 'https://corporate.ford.com/investors/reports-and-filings.html',
            'stellantis': 'https://www.stellantis.com/en/investors/financial-reports',
            'hyundai': 'https://www.hyundaimotorgroup.com/MediaCenter/AnnualReport.hub',
            'gm': 'https://investor.gm.com/financial-reports/annual-reports'
        }
    }
}

# =============================================================================
# Métriques clés à collecter
# =============================================================================
KEY_METRICS = {
    'production_metrics': [
        'total_production_volume',
        'production_by_vehicle_type',
        'production_capacity_utilization',
        'market_share_by_manufacturer',
        'regional_production_distribution'
    ],
    
    'economic_metrics': [
        'average_vehicle_price',
        'production_costs',
        'export_volumes',
        'import_volumes',
        'trade_balance',
        'tariff_impact_on_prices'
    ],
    
    'transition_metrics': [
        'ev_production_share',
        'hybrid_production_share',
        'battery_capacity_production',
        'charging_infrastructure_growth',
        'ev_price_trends'
    ],
    
    'supply_chain_metrics': [
        'steel_price_index',
        'aluminum_price_index',
        'lithium_price_index',
        'copper_price_index',
        'shipping_costs',
        'supply_chain_disruption_index'
    ]
}

# =============================================================================
# Configuration pour l'analyse prédictive
# =============================================================================
FORECASTING_CONFIG = {
    'models_to_use': [
        'ARIMA',
        'Linear Regression',
        'XGBoost',
        'Facebook Prophet',
        'LSTM Neural Networks'
    ],
    
    'scenarios': {
        'baseline': {
            'description': 'Current trends continue',
            'tariff_changes': 0,
            'ev_adoption_rate': 'current_trend'
        },
        'high_tariffs': {
            'description': 'Increased US tariffs on automotive imports',
            'tariff_changes': 0.15,  # +15% additional tariffs
            'ev_adoption_rate': 'current_trend'
        },
        'accelerated_ev': {
            'description': 'Accelerated EV adoption due to policies',
            'tariff_changes': 0,
            'ev_adoption_rate': 'accelerated'
        },
        'combined_impact': {
            'description': 'High tariffs + accelerated EV adoption',
            'tariff_changes': 0.15,
            'ev_adoption_rate': 'accelerated'
        }
    },
    
    'forecast_variables': [
        'production_volume_by_manufacturer',
        'market_share_evolution',
        'average_vehicle_prices',
        'ev_market_penetration',
        'raw_material_costs',
        'trade_flow_volumes'
    ]
}

# =============================================================================
# Configuration des livrables
# =============================================================================
DELIVERABLES_CONFIG = {
    'excel_outputs': {
        'historical_analysis': {
            'filename': 'automotive_historical_analysis_{timestamp}.xlsx',
            'sheets': [
                'Production Trends 2010-2024',
                'Manufacturer Market Share',
                'Regional Analysis',
                'Trade Flows',
                'Raw Material Prices',
                'EV Transition Data'
            ]
        },
        
        'forecasting_results': {
            'filename': 'automotive_forecasts_2025-2030_{timestamp}.xlsx',
            'sheets': [
                'Production Forecasts',
                'Price Forecasts',
                'Market Share Projections',
                'Scenario Analysis',
                'Model Performance'
            ]
        },
        
        'policy_impact': {
            'filename': 'us_policy_impact_analysis_{timestamp}.xlsx',
            'sheets': [
                'Tariff Impact Analysis',
                'IRA Impact on EV Market',
                'Trade Flow Changes',
                'Cost Structure Analysis'
            ]
        }
    },
    
    'dashboard_data': {
        'filename': 'automotive_dashboard_data_{timestamp}.json',
        'structure': {
            'time_series_data': 'Production, prices, market share over time',
            'comparative_data': 'Manufacturer comparisons',
            'geographic_data': 'Regional production and trade data',
            'forecast_data': 'Predictive models results'
        }
    }
}

# =============================================================================
# Configuration de validation des données
# =============================================================================
DATA_VALIDATION = {
    'quality_checks': {
        'completeness_threshold': 0.8,  # 80% des données doivent être présentes
        'consistency_checks': True,
        'outlier_detection': True,
        'trend_validation': True
    },
    
    'data_sources_priority': {
        'production_data': ['oica', 'marklines', 'manufacturer_reports'],
        'trade_data': ['census_gov', 'wto', 'ustr'],
        'price_data': ['lme', 'steelorbis', 'benchmark_minerals'],
        'ev_data': ['ev_volumes', 'iea', 'manufacturer_reports']
    }
}

# =============================================================================
# URLs et endpoints spécifiques
# =============================================================================
SPECIFIC_URLS = {
    'oica_historical': {
        'base_url': 'https://www.oica.net/category/production-statistics/',
        'year_pattern': '{year}-statistics/',
        'years': list(range(2010, 2025))
    },
    
    'us_trade_data': {
        'census_imports': 'https://www.census.gov/foreign-trade/statistics/product/enduse/imports/',
        'census_exports': 'https://www.census.gov/foreign-trade/statistics/product/enduse/exports/',
        'automotive_codes': ['7810', '7820', '7830']  # HS codes for vehicles
    },
    
    'tariff_schedules': {
        'current_tariffs': 'https://hts.usitc.gov/current',
        'automotive_chapters': ['87']  # HTS Chapter 87: Vehicles
    },
    
    'raw_materials_apis': {
        'steel_api': 'https://api.steelorbis.com/prices',
        'lme_api': 'https://www.lme.com/api/prices',
        'fred_api': 'https://api.stlouisfed.org/fred/series'
    }
}

# =============================================================================
# Configuration des modèles de prévision
# =============================================================================
ML_CONFIG = {
    'features_for_prediction': [
        'historical_production',
        'gdp_growth',
        'steel_prices',
        'oil_prices',
        'exchange_rates',
        'policy_indicators',
        'ev_adoption_rate',
        'consumer_sentiment'
    ],
    
    'model_parameters': {
        'arima': {'order': (2, 1, 2), 'seasonal_order': (1, 1, 1, 12)},
        'xgboost': {'n_estimators': 100, 'max_depth': 6, 'learning_rate': 0.1},
        'prophet': {'yearly_seasonality': True, 'weekly_seasonality': False},
        'lstm': {'units': 50, 'epochs': 100, 'batch_size': 32}
    }
}
