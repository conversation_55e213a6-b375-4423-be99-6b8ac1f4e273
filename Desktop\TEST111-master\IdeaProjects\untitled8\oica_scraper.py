"""
OICA Production Statistics Scraper
"""

import os
import json
import pandas as pd
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from utils import (
    make_request, clean_numeric_value, get_file_name_from_url,
    sanitize_filename, build_year_url, is_valid_year
)
from config import BASE_URL, TABLE_COLUMNS, DOWNLOAD_EXTENSIONS, DATA_DIR, FILES_DIR


class OICAScraper:
    def __init__(self, year=2019):
        """Initialize scraper for specific year"""
        if not is_valid_year(year):
            raise ValueError(f"Year {year} is not available for scraping")
        
        self.year = year
        self.url = build_year_url(year)
        self.data = []
        self.download_links = []
    
    def scrape_production_table(self):
        """Scrape the main production statistics table"""
        print(f"Scraping production data for {self.year}...")
        
        response = make_request(self.url)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find the main statistics table
        table = soup.find('table') or self._find_table_alternative(soup)
        
        if not table:
            print("No table found, trying to extract from text...")
            return self._extract_from_text(soup)
        
        return self._parse_table(table)
    
    def _find_table_alternative(self, soup):
        """Alternative method to find table data"""
        # Look for table-like structures in divs or other elements
        for element in soup.find_all(['div', 'section']):
            if 'table' in element.get('class', []) or 'data' in element.get('class', []):
                return element
        return None
    
    def _extract_from_text(self, soup):
        """Extract data from text content when no table is found"""
        print("Extracting data from page text...")
        
        # Look for the production data in the page content
        content = soup.get_text()
        lines = content.split('\n')
        
        data_started = False
        for line in lines:
            line = line.strip()
            
            # Skip empty lines
            if not line:
                continue
            
            # Look for country data pattern
            if self._is_country_data_line(line):
                parsed_row = self._parse_country_line(line)
                if parsed_row:
                    self.data.append(parsed_row)
        
        print(f"Extracted {len(self.data)} rows from text")
        return self.data
    
    def _is_country_data_line(self, line):
        """Check if line contains country production data"""
        # Look for lines with country name followed by numbers
        parts = line.split()
        if len(parts) < 4:
            return False
        
        # Check if line has numeric data
        numeric_parts = 0
        for part in parts[-4:]:
            cleaned = part.replace(',', '').replace('%', '').replace('-', '').replace('+', '')
            if cleaned.replace('.', '').isdigit():
                numeric_parts += 1
        
        return numeric_parts >= 2
    
    def _parse_country_line(self, line):
        """Parse a line containing country production data"""
        parts = line.split()
        
        if len(parts) < 4:
            return None
        
        # Extract country name (everything except last 4 parts)
        country_parts = parts[:-4]
        country = ' '.join(country_parts)
        
        # Extract numeric data (last 4 parts)
        numeric_parts = parts[-4:]
        
        try:
            cars = clean_numeric_value(numeric_parts[0])
            commercial = clean_numeric_value(numeric_parts[1])
            total = clean_numeric_value(numeric_parts[2])
            change = clean_numeric_value(numeric_parts[3])
            
            return {
                'country_region': country,
                'cars': cars,
                'commercial_vehicles': commercial,
                'total': total,
                'percent_change': change,
                'year': self.year
            }
        except (IndexError, ValueError):
            return None
    
    def _parse_table(self, table):
        """Parse HTML table"""
        print("Parsing HTML table...")
        
        rows = table.find_all('tr')
        headers = []
        
        # Extract headers
        header_row = rows[0] if rows else None
        if header_row:
            headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
        
        # Extract data rows
        for row in rows[1:]:
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 4:  # Minimum expected columns
                row_data = {}
                
                for i, cell in enumerate(cells):
                    if i < len(headers):
                        header = headers[i]
                        value = cell.get_text(strip=True)
                        
                        # Map to standard column names
                        column_name = TABLE_COLUMNS.get(header, header.lower().replace(' ', '_'))
                        
                        # Clean numeric values
                        if column_name in ['cars', 'commercial_vehicles', 'total', 'percent_change']:
                            value = clean_numeric_value(value)
                        
                        row_data[column_name] = value
                
                row_data['year'] = self.year
                self.data.append(row_data)
        
        print(f"Extracted {len(self.data)} rows from table")
        return self.data
    
    def find_download_links(self):
        """Find PDF and Excel download links"""
        print("Finding download links...")
        
        response = make_request(self.url)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find all links
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link['href']
            
            # Check if link points to downloadable file
            for ext in DOWNLOAD_EXTENSIONS:
                if ext.lower() in href.lower():
                    full_url = urljoin(BASE_URL, href)
                    filename = get_file_name_from_url(full_url)
                    
                    self.download_links.append({
                        'url': full_url,
                        'filename': sanitize_filename(filename),
                        'type': ext.replace('.', ''),
                        'description': link.get_text(strip=True)
                    })
        
        print(f"Found {len(self.download_links)} download links")
        return self.download_links
    
    def download_files(self):
        """Download PDF and Excel files"""
        if not self.download_links:
            self.find_download_links()
        
        print("Downloading files...")
        downloaded_files = []
        
        for link_info in self.download_links:
            try:
                response = make_request(link_info['url'])
                
                filename = f"{self.year}_{link_info['filename']}"
                filepath = os.path.join(FILES_DIR, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                print(f"Downloaded: {filename}")
                downloaded_files.append(filepath)
                
            except Exception as e:
                print(f"Failed to download {link_info['filename']}: {e}")
        
        return downloaded_files
    
    def save_data(self, format='both'):
        """Save scraped data to files"""
        if not self.data:
            print("No data to save")
            return
        
        base_filename = f"oica_production_{self.year}"
        
        # Save as JSON
        if format in ['json', 'both']:
            json_path = os.path.join(DATA_DIR, f"{base_filename}.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, indent=2, ensure_ascii=False)
            print(f"Data saved to: {json_path}")
        
        # Save as CSV
        if format in ['csv', 'both']:
            csv_path = os.path.join(DATA_DIR, f"{base_filename}.csv")
            df = pd.DataFrame(self.data)
            df.to_csv(csv_path, index=False, encoding='utf-8')
            print(f"Data saved to: {csv_path}")
    
    def get_summary(self):
        """Get summary of scraped data"""
        if not self.data:
            return "No data available"
        
        df = pd.DataFrame(self.data)
        
        summary = {
            'year': self.year,
            'total_countries': len(df),
            'total_cars_production': df['cars'].sum() if 'cars' in df.columns else 0,
            'total_commercial_production': df['commercial_vehicles'].sum() if 'commercial_vehicles' in df.columns else 0,
            'total_production': df['total'].sum() if 'total' in df.columns else 0,
            'download_links_found': len(self.download_links)
        }
        
        return summary
