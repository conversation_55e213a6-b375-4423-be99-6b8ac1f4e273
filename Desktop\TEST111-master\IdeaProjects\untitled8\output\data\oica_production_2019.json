[{"country_region": "Argentina", "cars": 108364, "commercial_vehicles": 206423, "total": 314787, "percent_change": -325, "year": 2019}, {"country_region": "Austria", "cars": 158400, "commercial_vehicles": 21000, "total": 179400, "percent_change": 88, "year": 2019}, {"country_region": "Belgium", "cars": 247020, "commercial_vehicles": 38777, "total": 285797, "percent_change": -74, "year": 2019}, {"country_region": "Brazil", "cars": 2448490, "commercial_vehicles": 496498, "total": 2944988, "percent_change": 22, "year": 2019}, {"country_region": "Canada", "cars": 461370, "commercial_vehicles": 1455215, "total": 1916585, "percent_change": -54, "year": 2019}, {"country_region": "China", "cars": 21360193, "commercial_vehicles": 4360472, "total": 25720665, "percent_change": -75, "year": 2019}, {"country_region": "Czech Rep.", "cars": 1427563, "commercial_vehicles": 6400, "total": 1433963, "percent_change": -6, "year": 2019}, {"country_region": "Egypt", "cars": 18500, "commercial_vehicles": 0, "total": 18500, "percent_change": 0, "year": 2019}, {"country_region": "Finland", "cars": 114785, "commercial_vehicles": 0, "total": 114785, "percent_change": 25, "year": 2019}, {"country_region": "France", "cars": 1675198, "commercial_vehicles": 527262, "total": 2202460, "percent_change": -29, "year": 2019}, {"country_region": "Germany", "cars": 4661328, "commercial_vehicles": 0, "total": 4661328, "percent_change": -9, "year": 2019}, {"country_region": "Hungary", "cars": 498158, "commercial_vehicles": 0, "total": 498158, "percent_change": 76, "year": 2019}, {"country_region": "India", "cars": 3623335, "commercial_vehicles": 892682, "total": 4516017, "percent_change": -122, "year": 2019}, {"country_region": "Indonesia", "cars": 1045666, "commercial_vehicles": 241182, "total": 1286848, "percent_change": -42, "year": 2019}, {"country_region": "Iran", "cars": 770000, "commercial_vehicles": 51060, "total": 821060, "percent_change": -25, "year": 2019}, {"country_region": "Italy", "cars": 542007, "commercial_vehicles": 373298, "total": 915305, "percent_change": -138, "year": 2019}, {"country_region": "Japan", "cars": 8328756, "commercial_vehicles": 1355542, "total": 9684298, "percent_change": -5, "year": 2019}, {"country_region": "Malaysia", "cars": 534115, "commercial_vehicles": 37517, "total": 571632, "percent_change": 12, "year": 2019}, {"country_region": "Morocco", "cars": 360110, "commercial_vehicles": 34542, "total": 394652, "percent_change": -18, "year": 2019}, {"country_region": "Mexico", "cars": 1382714, "commercial_vehicles": 2604080, "total": 3986794, "percent_change": -28, "year": 2019}, {"country_region": "Poland", "cars": 434700, "commercial_vehicles": 215164, "total": 649864, "percent_change": -15, "year": 2019}, {"country_region": "Portugal", "cars": 282142, "commercial_vehicles": 63562, "total": 345704, "percent_change": 174, "year": 2019}, {"country_region": "Romania", "cars": 490412, "commercial_vehicles": 0, "total": 490412, "percent_change": 29, "year": 2019}, {"country_region": "Russia", "cars": 1523594, "commercial_vehicles": 196190, "total": 1719784, "percent_change": -28, "year": 2019}, {"country_region": "Serbia", "cars": 34985, "commercial_vehicles": 130, "total": 35115, "percent_change": -378, "year": 2019}, {"country_region": "Slovakia", "cars": 1100000, "commercial_vehicles": 0, "total": 1100000, "percent_change": 6, "year": 2019}, {"country_region": "Slovenia", "cars": 199102, "commercial_vehicles": 0, "total": 199102, "percent_change": -49, "year": 2019}, {"country_region": "South Africa", "cars": 348665, "commercial_vehicles": 283318, "total": 631983, "percent_change": 35, "year": 2019}, {"country_region": "South Korea", "cars": 3612587, "commercial_vehicles": 338030, "total": 3950617, "percent_change": -19, "year": 2019}, {"country_region": "Spain", "cars": 2248019, "commercial_vehicles": 574336, "total": 2822355, "percent_change": 1, "year": 2019}, {"country_region": "Taiwan", "cars": 189549, "commercial_vehicles": 61755, "total": 251304, "percent_change": -8, "year": 2019}, {"country_region": "Thailand", "cars": 795254, "commercial_vehicles": 1218456, "total": 2013710, "percent_change": -71, "year": 2019}, {"country_region": "Turkey", "cars": 982642, "commercial_vehicles": 478602, "total": 1461244, "percent_change": -57, "year": 2019}, {"country_region": "Ukraine", "cars": 6254, "commercial_vehicles": 1011, "total": 7265, "percent_change": 97, "year": 2019}, {"country_region": "UK", "cars": 1303135, "commercial_vehicles": 78270, "total": 1381405, "percent_change": -139, "year": 2019}, {"country_region": "USA", "cars": 2512780, "commercial_vehicles": 8367239, "total": 10880019, "percent_change": -37, "year": 2019}, {"country_region": "Uzbekistan", "cars": 271113, "commercial_vehicles": 0, "total": 271113, "percent_change": 229, "year": 2019}, {"country_region": "Others", "cars": 1048191, "commercial_vehicles": 59652, "total": 1108503, "percent_change": null, "year": 2019}, {"country_region": "Total", "cars": 67149196, "commercial_vehicles": 24637665, "total": 91786861, "percent_change": -52, "year": 2019}]