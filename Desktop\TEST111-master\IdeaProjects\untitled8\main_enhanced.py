"""
Enhanced Main Script for Multi-Source Automotive Data Scraper
Orchestrates data collection from all sources and generates comprehensive Excel report
"""

import argparse
import sys
import logging
from datetime import datetime
import os

# Setup logging
from enhanced_config import LOGGING_CONFIG

logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG['file']),
        logging.StreamHandler(sys.stdout)
    ]
)

from data_consolidator import AutomotiveDataConsolidator
from excel_generator import AutomotiveExcelGenerator
from enhanced_config import OICA_CONFIG

def main():
    """Main function to orchestrate the entire data collection and reporting process"""
    parser = argparse.ArgumentParser(
        description='Enhanced Multi-Source Automotive Data Scraper',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_enhanced.py --years 2022 2023 2024
  python main_enhanced.py --sources oica trade --download-files
  python main_enhanced.py --quick-report
  python main_enhanced.py --years 2023 --excel-only
        """
    )
    
    # Year selection
    parser.add_argument('--years', type=int, nargs='+', 
                       default=[datetime.now().year - i for i in range(3)],
                       help='Years to scrape data for (default: last 3 years)')
    
    # Source selection
    parser.add_argument('--sources', choices=['oica', 'trade', 'materials', 'automaker', 'all'], 
                       nargs='+', default=['all'],
                       help='Data sources to scrape (default: all)')
    
    # File options
    parser.add_argument('--download-files', action='store_true',
                       help='Download PDF and Excel files from sources')
    
    parser.add_argument('--excel-only', action='store_true',
                       help='Only generate Excel report from existing data')
    
    parser.add_argument('--output-format', choices=['excel', 'json', 'csv', 'all'], 
                       default='excel',
                       help='Output format (default: excel)')
    
    # Quick options
    parser.add_argument('--quick-report', action='store_true',
                       help='Generate quick report with limited data')
    
    parser.add_argument('--list-years', action='store_true',
                       help='List available years for OICA data')
    
    # Advanced options
    parser.add_argument('--validate-only', action='store_true',
                       help='Only validate existing data without scraping')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger = logging.getLogger(__name__)
    
    # List available years
    if args.list_years:
        print("Available years for OICA data:")
        for year in sorted(OICA_CONFIG['available_years'], reverse=True):
            print(f"  {year}")
        return
    
    # Validate years
    invalid_years = [year for year in args.years if year not in OICA_CONFIG['available_years']]
    if invalid_years:
        logger.warning(f"Invalid years for OICA data: {invalid_years}")
        args.years = [year for year in args.years if year in OICA_CONFIG['available_years']]
        if not args.years:
            logger.error("No valid years specified")
            sys.exit(1)
    
    logger.info(f"Starting enhanced automotive data scraper")
    logger.info(f"Years: {args.years}")
    logger.info(f"Sources: {args.sources}")
    
    try:
        # Initialize consolidator
        consolidator = AutomotiveDataConsolidator()
        
        if not args.excel_only and not args.validate_only:
            # Collect data from all sources
            logger.info("Starting data collection...")
            
            # Determine which sources to scrape
            sources_to_scrape = args.sources
            if 'all' in sources_to_scrape:
                sources_to_scrape = ['oica', 'trade', 'materials', 'automaker']
            
            # Collect data
            consolidated_data = consolidator.collect_all_data(
                years=args.years, 
                download_files=args.download_files
            )
            
            logger.info("Data collection completed")
            
            # Save consolidated data
            consolidator.save_consolidated_data()
            
        elif args.validate_only:
            # Load existing data and validate
            logger.info("Validating existing data...")
            # Implementation for loading and validating existing data
            logger.info("Data validation completed")
            return
        
        # Create unified datasets
        logger.info("Creating unified datasets...")
        unified_datasets = consolidator.create_unified_datasets()
        
        # Get collection summary
        summary_data = consolidator.get_collection_summary()
        
        # Generate outputs based on format selection
        if args.output_format in ['excel', 'all']:
            logger.info("Generating Excel report...")
            excel_generator = AutomotiveExcelGenerator()
            
            if args.quick_report:
                # Create simple report
                excel_file = excel_generator.create_simple_report(unified_datasets)
            else:
                # Create comprehensive report
                excel_file = excel_generator.create_comprehensive_report(
                    consolidator.consolidated_data, 
                    unified_datasets, 
                    summary_data
                )
            
            logger.info(f"Excel report generated: {excel_file}")
            print(f"\n✅ Excel report created: {excel_file}")
        
        if args.output_format in ['json', 'all']:
            # Save as JSON
            json_file = consolidator.save_consolidated_data()
            logger.info(f"JSON data saved: {json_file}")
            print(f"✅ JSON data saved: {json_file}")
        
        if args.output_format in ['csv', 'all']:
            # Save unified datasets as CSV
            logger.info("Saving CSV files...")
            csv_files = save_datasets_as_csv(unified_datasets)
            for csv_file in csv_files:
                logger.info(f"CSV file saved: {csv_file}")
                print(f"✅ CSV file saved: {csv_file}")
        
        # Print summary
        print_collection_summary(summary_data)
        
        logger.info("Enhanced automotive data scraper completed successfully")
        
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error in main process: {e}", exc_info=True)
        sys.exit(1)

def save_datasets_as_csv(unified_datasets):
    """Save unified datasets as CSV files"""
    from enhanced_config import DATA_DIR
    
    csv_files = []
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    for dataset_name, df in unified_datasets.items():
        if isinstance(df, pd.DataFrame) and not df.empty:
            filename = f"automotive_{dataset_name}_{timestamp}.csv"
            filepath = os.path.join(DATA_DIR, filename)
            df.to_csv(filepath, index=False, encoding='utf-8')
            csv_files.append(filepath)
    
    return csv_files

def print_collection_summary(summary_data):
    """Print a formatted summary of the data collection"""
    print("\n" + "="*60)
    print("📊 DATA COLLECTION SUMMARY")
    print("="*60)
    
    print(f"Collection Date: {summary_data.get('collection_date', 'Unknown')}")
    print(f"Total Records: {summary_data.get('total_records', 0):,}")
    print(f"Sources Collected: {summary_data.get('sources_collected', 0)}")
    
    # Source summaries
    if 'source_summaries' in summary_data:
        print("\n📈 Source Details:")
        for source, data in summary_data['source_summaries'].items():
            print(f"  {source.upper()}:")
            print(f"    Records: {data.get('total_records', 0):,}")
            
            years = data.get('years_covered', [])
            if years:
                print(f"    Years: {min(years)}-{max(years)}")
            
            if 'countries_covered' in data:
                print(f"    Countries: {data['countries_covered']}")
    
    # Data quality
    if 'data_quality' in summary_data:
        print("\n🔍 Data Quality:")
        for source, quality in summary_data['data_quality'].items():
            status = quality.get('status', 'Unknown')
            emoji = "✅" if status == 'valid' else "⚠️" if status == 'warning' else "❌"
            print(f"  {source.upper()}: {emoji} {status}")
            
            if quality.get('issues'):
                for issue in quality['issues'][:2]:  # Show first 2 issues
                    print(f"    - {issue}")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    main()
