"""
Main script for generating unified automotive data Excel file
Creates the exact format requested:
Year | Country | Manufacturer | VehicleType | ProductionVolume | TariffRate_USA | SteelPrice | EV_Share | ImportVolume
"""

import argparse
import sys
import logging
from datetime import datetime

# Setup logging
from enhanced_config import LOGGING_CONFIG

logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG['file']),
        logging.StreamHandler(sys.stdout)
    ]
)

from unified_data_generator import UnifiedAutomotiveDataGenerator

def main():
    """Main function for unified data generation"""
    parser = argparse.ArgumentParser(
        description='Unified Automotive Data Generator',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_unified.py
  python main_unified.py --years 2022 2023 2024
  python main_unified.py --output-file my_automotive_data.xlsx
  python main_unified.py --verbose
        """
    )
    
    # Year selection
    parser.add_argument('--years', type=int, nargs='+', 
                       default=[2023, 2024],
                       help='Years to generate data for (default: 2023, 2024)')
    
    # Output options
    parser.add_argument('--output-file', type=str,
                       help='Custom output filename (optional)')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    parser.add_argument('--sample-only', action='store_true',
                       help='Generate sample data only (no web scraping)')
    
    args = parser.parse_args()
    
    # Setup logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger = logging.getLogger(__name__)
    
    logger.info(f"Starting unified automotive data generation")
    logger.info(f"Years: {args.years}")
    
    try:
        # Initialize generator
        generator = UnifiedAutomotiveDataGenerator()
        
        # Generate unified dataset
        logger.info("Generating unified automotive dataset...")
        df = generator.generate_unified_dataset(args.years)
        
        if df.empty:
            logger.error("No data generated")
            sys.exit(1)
        
        # Save to Excel
        logger.info("Saving to Excel...")
        excel_file = generator.save_to_excel(df, args.output_file)
        
        # Get summary statistics
        summary = generator.get_summary_stats(df)
        
        # Print results
        print_results(excel_file, summary)
        
        logger.info("Unified automotive data generation completed successfully")
        
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error in main process: {e}", exc_info=True)
        sys.exit(1)

def print_results(excel_file, summary):
    """Print formatted results"""
    print("\n" + "="*80)
    print("🚀 UNIFIED AUTOMOTIVE DATA GENERATED SUCCESSFULLY")
    print("="*80)
    
    print(f"📁 Excel File: {excel_file}")
    print(f"📊 Total Records: {summary['total_records']:,}")
    print(f"📅 Years Covered: {summary['years_covered']}")
    print(f"🌍 Countries: {summary['countries_covered']}")
    print(f"🏭 Manufacturers: {summary['manufacturers_covered']}")
    print(f"🚗 Vehicle Types: {summary['vehicle_types']}")
    
    print(f"\n📈 Key Statistics:")
    print(f"  Total Production: {summary['total_production']:,} vehicles")
    print(f"  Total US Imports: {summary['total_imports']:,} vehicles")
    print(f"  Average EV Share: {summary['avg_ev_share']:.1%}")
    print(f"  Average Steel Price: ${summary['avg_steel_price']:.0f}")
    
    print(f"\n🏆 Top 5 Manufacturers by Production:")
    for i, (manufacturer, volume) in enumerate(summary['top_producers'].items(), 1):
        print(f"  {i}. {manufacturer}: {volume:,} vehicles")
    
    print(f"\n📋 Excel Structure:")
    print("  Columns: Year | Country | Manufacturer | VehicleType | ProductionVolume | TariffRate_USA | SteelPrice | EV_Share | ImportVolume")
    print("  Format: Professional formatting with proper number/percentage formats")
    
    print("\n" + "="*80)
    print("✅ Ready to use! Open the Excel file to view your automotive data.")
    print("="*80)

def create_sample_preview():
    """Create and display a sample of the data structure"""
    from enhanced_config import UNIFIED_EXCEL_CONFIG
    
    sample = UNIFIED_EXCEL_CONFIG['sample_data']
    
    print("\n📋 Sample Data Structure:")
    print("-" * 120)
    print(f"{'Year':<6} {'Country':<12} {'Manufacturer':<12} {'VehicleType':<12} {'ProductionVolume':<16} {'TariffRate_USA':<14} {'SteelPrice':<10} {'EV_Share':<9} {'ImportVolume':<12}")
    print("-" * 120)
    tariff_rate = f"{sample['TariffRate_USA']:.1%}"
    ev_share = f"{sample['EV_Share']:.1%}"
    print(f"{sample['Year']:<6} {sample['Country']:<12} {sample['Manufacturer']:<12} {sample['VehicleType']:<12} {sample['ProductionVolume']:,<16} {tariff_rate:<14} ${sample['SteelPrice']:<9} {ev_share:<9} {sample['ImportVolume']:,<12}")
    print("-" * 120)

if __name__ == "__main__":
    # Show sample structure
    create_sample_preview()
    
    # Run main function
    main()
