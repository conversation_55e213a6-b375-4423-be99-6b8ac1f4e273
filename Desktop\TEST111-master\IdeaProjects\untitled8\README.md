# Enhanced Multi-Source Automotive Data Scraper

Un scraper Python avancé pour extraire et consolider des données automobiles depuis plusieurs sources internationales, générant un rapport Excel complet avec analyses et visualisations.

## 🌐 Sources de données supportées

- **OICA** (Organisation Internationale des Constructeurs d'Automobiles) - Statistiques de production
- **USTR** (U.S. Trade Representative) - Données commerciales américaines
- **WTO** (World Trade Organization) - Statistiques commerciales mondiales
- **LME/Investing.com/FRED** - Indices de prix des matières premières
- **Rapports d'entreprises** - Données financières des constructeurs automobiles

## ✅ Fonctionnalités avancées

- ✅ Collecte multi-sources automatisée
- ✅ Validation et nettoyage des données
- ✅ Consolidation intelligente des datasets
- ✅ Génération de rapports Excel professionnels
- ✅ Analyses de tendances et corrélations
- ✅ Gestion d'erreurs robuste avec logging
- ✅ Support de multiples années (1999-2024)
- ✅ Téléchargement automatique des fichiers
- ✅ Interface en ligne de commande avancée

## 🚀 Installation et utilisation

### Installation des dépendances
```bash
pip install -r requirements.txt
```

### Utilisation rapide
```bash
# Collecte complète avec rapport Excel (recommandé)
python main_enhanced.py

# Collecte pour des années spécifiques
python main_enhanced.py --years 2022 2023 2024

# Collecte avec téléchargement de fichiers
python main_enhanced.py --download-files

# Rapport rapide (données limitées)
python main_enhanced.py --quick-report

# 3. Test avec téléchargement des fichiers
python main.py --year 2020 --download-files
```

## 📊 Résultats obtenus

Le scraper a été testé avec succès et a extrait :

### Pour 2019 :
- **39 pays/régions**
- **67,149,196 voitures** produites
- **24,637,665 véhicules commerciaux**
- **91,786,861 véhicules au total**

### Pour 2020 :
- **40 pays/régions**
- **16 fichiers PDF/Excel** téléchargés
- Données complètes avec pourcentages de changement

## 📁 Structure des fichiers générés

```
output/
├── data/
│   ├── oica_production_2019.csv    # Données CSV
│   ├── oica_production_2019.json   # Données JSON
│   ├── oica_production_2020.csv
│   └── oica_production_2020.json
└── files/
    ├── 2020_By-country-region-2020.pdf
    ├── 2020_By-country-region-2020.xlsx
    ├── 2020_Passenger-Cars-2020.pdf
    ├── 2020_Passenger-Cars-2020.xlsx
    ├── 2020_Light-Commercial-Vehicles-2020.pdf
    ├── 2020_Heavy-Trucks-2020.pdf
    └── 2020_Buses-and-Coaches-2020.pdf
```

## 🛠️ Utilisation avancée

```bash
# Lister toutes les années disponibles
python main.py --list-years

# Scraper une année spécifique
python main.py --year 2021

# Exporter seulement en CSV
python main.py --year 2019 --format csv

# Exporter seulement en JSON
python main.py --year 2019 --format json

# Scraper avec téléchargement des fichiers
python main.py --year 2022 --download-files
```

## 📋 Format des données extraites

### Colonnes CSV/JSON :
- `country_region` : Nom du pays/région
- `cars` : Production de voitures particulières
- `commercial_vehicles` : Production de véhicules commerciaux
- `total` : Production totale
- `percent_change` : Pourcentage de changement vs année précédente
- `year` : Année des données

### Exemple de données :
```csv
country_region,cars,commercial_vehicles,total,percent_change,year
China,21360193,4360472,25720665,-7.5,2019
USA,2512780,8367239,10880019,-3.7,2019
Japan,8328756,1355542,9684298,-0.5,2019
```

## ⚙️ Configuration

Modifiez `config.py` pour :
- Changer les délais entre requêtes
- Ajouter de nouveaux en-têtes HTTP
- Modifier les répertoires de sortie
- Ajuster les paramètres de retry

## 🔧 Fichiers du projet

- `main.py` - Point d'entrée principal
- `oica_scraper.py` - Classe principale du scraper
- `config.py` - Configuration et constantes
- `utils.py` - Fonctions utilitaires
- `requirements.txt` - Dépendances Python

## 🚨 Limitations et bonnes pratiques

- Respecte les délais entre requêtes (1 seconde par défaut)
- Gestion automatique des erreurs avec retry
- Validation des années disponibles
- Nettoyage automatique des données numériques

## 📈 Années disponibles

Le scraper supporte les années de **1999 à 2024**.

## 🎯 Cas d'usage

Ce scraper est parfait pour :
- Analyses économiques de l'industrie automobile
- Recherches académiques
- Rapports de marché
- Études comparatives par pays
- Analyses de tendances temporelles
