"""
U.S. Trade Representative (USTR) and WTO Data Scraper
Scrapes automotive trade data from USTR and WTO sources
"""

import os
import re
import json
import pandas as pd
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import logging
from datetime import datetime
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhanced_config import USTR_CONFIG, WTO_CONFIG, REQUEST_CONFIG, DATA_DIR
from utils import make_request, clean_numeric_value, sanitize_filename

class USTRScraper:
    """Scraper for U.S. Trade Representative automotive data"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data = []
        self.download_links = []
        
    def scrape_automotive_trade_data(self, years=None):
        """Scrape automotive trade data from USTR"""
        if years is None:
            years = [datetime.now().year - i for i in range(5)]  # Last 5 years
            
        self.logger.info(f"Scraping USTR automotive trade data for years: {years}")
        
        for year in years:
            try:
                self._scrape_year_data(year)
            except Exception as e:
                self.logger.error(f"Failed to scrape USTR data for {year}: {e}")
                
        return self.data
    
    def _scrape_year_data(self, year):
        """Scrape trade data for a specific year"""
        # Search for automotive trade reports
        search_urls = [
            f"{USTR_CONFIG['base_url']}/trade-agreements/free-trade-agreements",
            f"{USTR_CONFIG['base_url']}/issue-areas/industry-manufacturing/automotive"
        ]
        
        for url in search_urls:
            try:
                response = make_request(url)
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Find automotive-related links
                self._extract_automotive_links(soup, year)
                
                # Extract trade statistics from page content
                self._extract_trade_statistics(soup, year)
                
            except Exception as e:
                self.logger.error(f"Error scraping {url}: {e}")
    
    def _extract_automotive_links(self, soup, year):
        """Extract links to automotive trade documents"""
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link['href']
            text = link.get_text(strip=True).lower()
            
            # Check if link is automotive-related
            if any(term in text for term in USTR_CONFIG['search_terms']):
                if str(year) in text or str(year) in href:
                    full_url = urljoin(USTR_CONFIG['base_url'], href)
                    
                    # Check if it's a downloadable file
                    for ext in USTR_CONFIG['file_extensions']:
                        if ext.lower() in href.lower():
                            self.download_links.append({
                                'url': full_url,
                                'filename': sanitize_filename(f"ustr_{year}_{link.get_text(strip=True)[:50]}{ext}"),
                                'type': ext.replace('.', ''),
                                'year': year,
                                'description': link.get_text(strip=True),
                                'source': 'USTR'
                            })
    
    def _extract_trade_statistics(self, soup, year):
        """Extract trade statistics from page content"""
        # Look for tables with trade data
        tables = soup.find_all('table')
        
        for table in tables:
            self._parse_trade_table(table, year)
        
        # Look for text-based statistics
        self._extract_text_statistics(soup, year)
    
    def _parse_trade_table(self, table, year):
        """Parse trade data from HTML table"""
        try:
            rows = table.find_all('tr')
            headers = []
            
            # Extract headers
            if rows:
                header_row = rows[0]
                headers = [th.get_text(strip=True).lower() for th in header_row.find_all(['th', 'td'])]
            
            # Extract data rows
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    row_data = {'year': year, 'source': 'USTR'}
                    
                    for i, cell in enumerate(cells):
                        if i < len(headers):
                            header = headers[i]
                            value = cell.get_text(strip=True)
                            
                            # Clean and categorize data
                            if 'country' in header or 'nation' in header:
                                row_data['country'] = value
                            elif any(term in header for term in ['export', 'import', 'trade', 'value']):
                                row_data[header] = clean_numeric_value(value)
                            elif 'automotive' in header or 'vehicle' in header:
                                row_data['sector'] = 'automotive'
                                row_data['value'] = clean_numeric_value(value)
                    
                    if 'country' in row_data and any(k for k in row_data.keys() if 'value' in k or 'export' in k or 'import' in k):
                        self.data.append(row_data)
                        
        except Exception as e:
            self.logger.error(f"Error parsing trade table: {e}")
    
    def _extract_text_statistics(self, soup, year):
        """Extract trade statistics from text content"""
        text_content = soup.get_text()
        lines = text_content.split('\n')
        
        # Patterns to match trade statistics
        patterns = [
            r'automotive.*?(\$[\d,]+\.?\d*\s*(?:billion|million))',
            r'vehicle.*?exports.*?(\$[\d,]+\.?\d*\s*(?:billion|million))',
            r'auto.*?imports.*?(\$[\d,]+\.?\d*\s*(?:billion|million))',
            r'trade.*?balance.*?(\$[\d,]+\.?\d*\s*(?:billion|million))'
        ]
        
        for line in lines:
            line = line.strip().lower()
            if any(term in line for term in USTR_CONFIG['search_terms']):
                for pattern in patterns:
                    matches = re.findall(pattern, line, re.IGNORECASE)
                    if matches:
                        # Extract the monetary value
                        value_str = matches[0]
                        value = self._parse_monetary_value(value_str)
                        
                        if value:
                            self.data.append({
                                'year': year,
                                'source': 'USTR',
                                'metric': self._categorize_metric(line),
                                'value': value,
                                'unit': 'USD',
                                'description': line[:100]
                            })
    
    def _parse_monetary_value(self, value_str):
        """Parse monetary value from string like '$5.2 billion'"""
        try:
            # Remove $ and extract number
            number_str = re.sub(r'[^\d.]', '', value_str.split()[0])
            number = float(number_str)
            
            # Apply multiplier
            if 'billion' in value_str.lower():
                number *= 1_000_000_000
            elif 'million' in value_str.lower():
                number *= 1_000_000
                
            return number
        except:
            return None
    
    def _categorize_metric(self, text):
        """Categorize the type of trade metric"""
        text = text.lower()
        if 'export' in text:
            return 'exports'
        elif 'import' in text:
            return 'imports'
        elif 'balance' in text:
            return 'trade_balance'
        elif 'total' in text:
            return 'total_trade'
        else:
            return 'other'
    
    def save_data(self, filename=None):
        """Save scraped data to file"""
        if not self.data:
            self.logger.warning("No USTR data to save")
            return None
            
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ustr_trade_data_{timestamp}.json"
        
        filepath = os.path.join(DATA_DIR, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"USTR data saved to: {filepath}")
        return filepath
    
    def get_summary(self):
        """Get summary of scraped USTR data"""
        if not self.data:
            return "No USTR data available"
        
        df = pd.DataFrame(self.data)
        
        summary = {
            'source': 'USTR',
            'total_records': len(df),
            'years_covered': sorted(df['year'].unique().tolist()) if 'year' in df.columns else [],
            'countries_covered': len(df['country'].unique()) if 'country' in df.columns else 0,
            'metrics_available': df['metric'].unique().tolist() if 'metric' in df.columns else [],
            'download_links_found': len(self.download_links)
        }
        
        return summary


class WTOScraper:
    """Scraper for World Trade Organization automotive data"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data = []
        
    def scrape_automotive_trade_statistics(self, years=None):
        """Scrape automotive trade statistics from WTO"""
        if years is None:
            years = [datetime.now().year - i for i in range(3)]  # Last 3 years
            
        self.logger.info(f"Scraping WTO automotive trade statistics for years: {years}")
        
        # WTO trade statistics by HS codes (automotive)
        for hs_code in WTO_CONFIG['automotive_codes']:
            try:
                self._scrape_hs_code_data(hs_code, years)
            except Exception as e:
                self.logger.error(f"Failed to scrape WTO data for HS code {hs_code}: {e}")
                
        return self.data
    
    def _scrape_hs_code_data(self, hs_code, years):
        """Scrape trade data for specific HS code"""
        # WTO doesn't have a direct API, so we'll scrape from their statistics pages
        base_url = WTO_CONFIG['base_url']
        
        for endpoint in WTO_CONFIG['data_endpoints']:
            try:
                url = base_url + endpoint
                response = make_request(url)
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Look for automotive trade data
                self._extract_wto_statistics(soup, hs_code, years)
                
            except Exception as e:
                self.logger.error(f"Error scraping WTO endpoint {endpoint}: {e}")
    
    def _extract_wto_statistics(self, soup, hs_code, years):
        """Extract trade statistics from WTO page"""
        # Look for tables with trade statistics
        tables = soup.find_all('table')
        
        for table in tables:
            self._parse_wto_table(table, hs_code, years)
    
    def _parse_wto_table(self, table, hs_code, years):
        """Parse WTO trade data table"""
        try:
            rows = table.find_all('tr')
            headers = []
            
            if rows:
                header_row = rows[0]
                headers = [th.get_text(strip=True).lower() for th in header_row.find_all(['th', 'td'])]
            
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    row_data = {'hs_code': hs_code, 'source': 'WTO'}
                    
                    for i, cell in enumerate(cells):
                        if i < len(headers):
                            header = headers[i]
                            value = cell.get_text(strip=True)
                            
                            if 'year' in header:
                                year = clean_numeric_value(value)
                                if year and year in years:
                                    row_data['year'] = year
                            elif 'country' in header:
                                row_data['country'] = value
                            elif any(term in header for term in ['value', 'trade', 'export', 'import']):
                                row_data[header] = clean_numeric_value(value)
                    
                    if 'year' in row_data and 'country' in row_data:
                        self.data.append(row_data)
                        
        except Exception as e:
            self.logger.error(f"Error parsing WTO table: {e}")
    
    def save_data(self, filename=None):
        """Save WTO data to file"""
        if not self.data:
            self.logger.warning("No WTO data to save")
            return None
            
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"wto_trade_data_{timestamp}.json"
        
        filepath = os.path.join(DATA_DIR, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"WTO data saved to: {filepath}")
        return filepath
    
    def get_summary(self):
        """Get summary of scraped WTO data"""
        if not self.data:
            return "No WTO data available"
        
        df = pd.DataFrame(self.data)
        
        summary = {
            'source': 'WTO',
            'total_records': len(df),
            'years_covered': sorted(df['year'].unique().tolist()) if 'year' in df.columns else [],
            'countries_covered': len(df['country'].unique()) if 'country' in df.columns else 0,
            'hs_codes_covered': df['hs_code'].unique().tolist() if 'hs_code' in df.columns else []
        }
        
        return summary
