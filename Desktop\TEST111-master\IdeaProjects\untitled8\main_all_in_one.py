"""
Script principal pour générer UN SEUL fichier Excel avec TOUTES les données
Combine toutes les sources dans un fichier Excel unique avec plusieurs onglets
"""

import argparse
import sys
import logging
from datetime import datetime
import pandas as pd
import os

# Setup logging
from enhanced_config import LOGGING_CONFIG, EXCEL_DIR

logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG['file']),
        logging.StreamHandler(sys.stdout)
    ]
)

from project_scraper import ProjectDataCollector
from unified_data_generator import UnifiedAutomotiveDataGenerator

class AllInOneExcelGenerator:
    """Générateur d'un fichier Excel unique avec toutes les données"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def create_complete_excel_file(self, years=None):
        """Crée un fichier Excel complet avec tous les onglets"""
        if years is None:
            years = [2023, 2024]
        
        self.logger.info("🚀 Création du fichier Excel complet avec toutes les données")
        
        # 1. Collecter toutes les données du projet
        self.logger.info("📡 Collecte des données du projet...")
        project_collector = ProjectDataCollector()
        project_data = project_collector.collect_all_project_data()
        
        # 2. Générer les données unifiées
        self.logger.info("🔄 Génération des données unifiées...")
        unified_generator = UnifiedAutomotiveDataGenerator()
        unified_df = unified_generator.generate_unified_dataset(years)
        
        # 3. Créer le fichier Excel unique
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"automotive_complete_analysis_{timestamp}.xlsx"
        filepath = os.path.join(EXCEL_DIR, filename)
        
        self.logger.info(f"📊 Création du fichier Excel: {filename}")
        
        with pd.ExcelWriter(filepath, engine='xlsxwriter') as writer:
            # Obtenir le workbook pour le formatage
            workbook = writer.book
            
            # Créer les formats
            formats = self._create_formats(workbook)
            
            # ONGLET 1: Données unifiées (format demandé)
            self._create_unified_sheet(writer, unified_df, formats)
            
            # ONGLET 2: Production historique (2010-2024)
            self._create_production_sheet(writer, project_data['production_historical'], formats)
            
            # ONGLET 3: Données commerciales et tarifs
            self._create_trade_sheet(writer, project_data['trade_data'], formats)
            
            # ONGLET 4: Prix matières premières
            self._create_materials_sheet(writer, project_data['raw_materials'], formats)
            
            # ONGLET 5: Transition EV
            self._create_ev_sheet(writer, project_data['ev_transition'], formats)
            
            # ONGLET 6: Données financières constructeurs
            self._create_financial_sheet(writer, project_data['manufacturer_financials'], formats)
            
            # ONGLET 7: Politiques économiques
            self._create_policy_sheet(writer, project_data['policy_data'], formats)
            
            # ONGLET 8: Résumé exécutif
            self._create_summary_sheet(writer, project_data, unified_df, formats)
        
        self.logger.info(f"✅ Fichier Excel complet créé: {filepath}")
        return filepath
    
    def _create_formats(self, workbook):
        """Crée les formats pour le workbook"""
        return {
            'header': workbook.add_format({
                'bold': True,
                'bg_color': '#4472C4',
                'font_color': 'white',
                'border': 1,
                'align': 'center',
                'valign': 'vcenter'
            }),
            'title': workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'bg_color': '#D9E2F3'
            }),
            'subtitle': workbook.add_format({
                'bold': True,
                'font_size': 12,
                'bg_color': '#E7E6E6'
            }),
            'number': workbook.add_format({
                'num_format': '#,##0',
                'border': 1,
                'align': 'right'
            }),
            'percentage': workbook.add_format({
                'num_format': '0.0%',
                'border': 1,
                'align': 'right'
            }),
            'currency': workbook.add_format({
                'num_format': '$#,##0',
                'border': 1,
                'align': 'right'
            }),
            'text': workbook.add_format({
                'border': 1,
                'align': 'center'
            }),
            'date': workbook.add_format({
                'num_format': 'yyyy-mm-dd',
                'border': 1,
                'align': 'center'
            })
        }
    
    def _create_unified_sheet(self, writer, unified_df, formats):
        """Crée l'onglet avec les données unifiées (format demandé)"""
        sheet_name = '1_Unified_Data'
        unified_df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=2)
        
        worksheet = writer.sheets[sheet_name]
        
        # Titre
        worksheet.merge_range('A1:I1', 'DONNÉES AUTOMOBILES UNIFIÉES', formats['title'])
        
        # Headers avec formatage
        for col_num, column in enumerate(unified_df.columns):
            worksheet.write(2, col_num, column, formats['header'])
        
        # Formatage des données
        for row_num in range(len(unified_df)):
            for col_num, column in enumerate(unified_df.columns):
                value = unified_df.iloc[row_num, col_num]
                cell_row = row_num + 3
                
                if column in ['TariffRate_USA', 'EV_Share']:
                    worksheet.write(cell_row, col_num, value, formats['percentage'])
                elif column in ['ProductionVolume', 'ImportVolume']:
                    worksheet.write(cell_row, col_num, value, formats['number'])
                elif column == 'SteelPrice':
                    worksheet.write(cell_row, col_num, value, formats['currency'])
                else:
                    worksheet.write(cell_row, col_num, value, formats['text'])
        
        # Auto-ajuster les colonnes
        for col_num, column in enumerate(unified_df.columns):
            max_length = max(len(str(column)), 15)
            worksheet.set_column(col_num, col_num, max_length)
    
    def _create_production_sheet(self, writer, production_data, formats):
        """Crée l'onglet production historique"""
        if not production_data:
            return
        
        sheet_name = '2_Production_Historical'
        df = pd.DataFrame(production_data)
        df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=2)
        
        worksheet = writer.sheets[sheet_name]
        worksheet.merge_range('A1:F1', 'PRODUCTION AUTOMOBILE HISTORIQUE (2010-2024)', formats['title'])
        
        # Headers
        for col_num, column in enumerate(df.columns):
            worksheet.write(2, col_num, column, formats['header'])
        
        # Auto-ajuster
        worksheet.set_column('A:A', 20)  # Country
        worksheet.set_column('B:F', 15)  # Autres colonnes
    
    def _create_trade_sheet(self, writer, trade_data, formats):
        """Crée l'onglet données commerciales"""
        sheet_name = '3_Trade_Data'
        
        # Créer des DataFrames pour chaque type de données commerciales
        row = 0
        
        if trade_data.get('tariffs'):
            df_tariffs = pd.DataFrame(trade_data['tariffs'])
            df_tariffs.to_excel(writer, sheet_name=sheet_name, index=False, startrow=row+1)
            
            worksheet = writer.sheets[sheet_name]
            worksheet.write(row, 0, 'TARIFS DOUANIERS USA', formats['subtitle'])
            row += len(df_tariffs) + 3
        
        if trade_data.get('trade_flows'):
            df_flows = pd.DataFrame(trade_data['trade_flows'])
            df_flows.to_excel(writer, sheet_name=sheet_name, index=False, startrow=row+1)
            
            worksheet = writer.sheets[sheet_name]
            worksheet.write(row, 0, 'FLUX COMMERCIAUX', formats['subtitle'])
            row += len(df_flows) + 3
        
        if trade_data.get('policy_impacts'):
            df_policy = pd.DataFrame(trade_data['policy_impacts'])
            df_policy.to_excel(writer, sheet_name=sheet_name, index=False, startrow=row+1)
            
            worksheet = writer.sheets[sheet_name]
            worksheet.write(row, 0, 'IMPACTS POLITIQUES', formats['subtitle'])
    
    def _create_materials_sheet(self, writer, materials_data, formats):
        """Crée l'onglet matières premières"""
        if not materials_data:
            return
        
        sheet_name = '4_Raw_Materials'
        df = pd.DataFrame(materials_data)
        df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=2)
        
        worksheet = writer.sheets[sheet_name]
        worksheet.merge_range('A1:F1', 'PRIX MATIÈRES PREMIÈRES (2010-2024)', formats['title'])
        
        # Auto-ajuster
        worksheet.set_column('A:F', 15)
    
    def _create_ev_sheet(self, writer, ev_data, formats):
        """Crée l'onglet transition EV"""
        if not ev_data:
            return
        
        sheet_name = '5_EV_Transition'
        df = pd.DataFrame(ev_data)
        df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=2)
        
        worksheet = writer.sheets[sheet_name]
        worksheet.merge_range('A1:F1', 'TRANSITION VÉHICULES ÉLECTRIQUES', formats['title'])
        
        worksheet.set_column('A:F', 15)
    
    def _create_financial_sheet(self, writer, financial_data, formats):
        """Crée l'onglet données financières"""
        if not financial_data:
            return
        
        sheet_name = '6_Financial_Data'
        df = pd.DataFrame(financial_data)
        df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=2)
        
        worksheet = writer.sheets[sheet_name]
        worksheet.merge_range('A1:F1', 'DONNÉES FINANCIÈRES CONSTRUCTEURS', formats['title'])
        
        worksheet.set_column('A:F', 18)
    
    def _create_policy_sheet(self, writer, policy_data, formats):
        """Crée l'onglet politiques économiques"""
        if not policy_data:
            return
        
        sheet_name = '7_Economic_Policies'
        df = pd.DataFrame(policy_data)
        df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=2)
        
        worksheet = writer.sheets[sheet_name]
        worksheet.merge_range('A1:F1', 'POLITIQUES ÉCONOMIQUES', formats['title'])
        
        worksheet.set_column('A:F', 20)
    
    def _create_summary_sheet(self, writer, project_data, unified_df, formats):
        """Crée l'onglet résumé exécutif"""
        sheet_name = '0_Executive_Summary'
        worksheet = writer.book.add_worksheet(sheet_name)
        writer.sheets[sheet_name] = worksheet
        
        # Titre principal
        worksheet.merge_range('A1:F1', 'RÉSUMÉ EXÉCUTIF - ANALYSE AUTOMOBILE COMPLÈTE', formats['title'])
        
        row = 3
        
        # Informations générales
        worksheet.write(row, 0, 'Date de génération:', formats['subtitle'])
        worksheet.write(row, 1, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        row += 2
        
        # Statistiques par catégorie
        worksheet.write(row, 0, 'DONNÉES COLLECTÉES:', formats['subtitle'])
        row += 1
        
        categories = {
            'Données unifiées': len(unified_df),
            'Production historique': len(project_data.get('production_historical', [])),
            'Données commerciales': len(project_data.get('trade_data', {}).get('trade_flows', [])),
            'Matières premières': len(project_data.get('raw_materials', [])),
            'Transition EV': len(project_data.get('ev_transition', [])),
            'Données financières': len(project_data.get('manufacturer_financials', [])),
            'Politiques économiques': len(project_data.get('policy_data', []))
        }
        
        for category, count in categories.items():
            worksheet.write(row, 0, category, formats['text'])
            worksheet.write(row, 1, f"{count:,} enregistrements", formats['number'])
            row += 1
        
        # Total
        total_records = sum(categories.values())
        row += 1
        worksheet.write(row, 0, 'TOTAL GÉNÉRAL:', formats['subtitle'])
        worksheet.write(row, 1, f"{total_records:,} enregistrements", formats['number'])
        
        # Auto-ajuster
        worksheet.set_column('A:A', 25)
        worksheet.set_column('B:F', 20)

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(
        description='Générateur de fichier Excel unique avec toutes les données automobiles'
    )
    
    parser.add_argument('--years', type=int, nargs='+', 
                       default=[2023, 2024],
                       help='Années pour les données unifiées (default: 2023, 2024)')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Mode verbose')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger = logging.getLogger(__name__)
    
    print("\n" + "="*80)
    print("🚗 GÉNÉRATION FICHIER EXCEL COMPLET - TOUTES DONNÉES AUTOMOBILES")
    print("="*80)
    print("📊 Ce fichier contiendra 8 onglets avec TOUTES les données:")
    print("  1. Données Unifiées (format demandé)")
    print("  2. Production Historique (2010-2024)")
    print("  3. Données Commerciales & Tarifs")
    print("  4. Prix Matières Premières")
    print("  5. Transition Véhicules Électriques")
    print("  6. Données Financières Constructeurs")
    print("  7. Politiques Économiques")
    print("  8. Résumé Exécutif")
    print("="*80)
    
    try:
        # Créer le générateur
        generator = AllInOneExcelGenerator()
        
        # Générer le fichier Excel complet
        excel_file = generator.create_complete_excel_file(args.years)
        
        # Afficher les résultats
        print("\n" + "="*80)
        print("✅ FICHIER EXCEL COMPLET GÉNÉRÉ AVEC SUCCÈS!")
        print("="*80)
        print(f"📁 Fichier: {excel_file}")
        print(f"📊 Contient: 8 onglets avec toutes les données")
        print(f"📅 Années unifiées: {args.years}")
        print(f"📈 Données historiques: 2010-2024")
        print("\n🎯 Votre fichier Excel unique est prêt à utiliser!")
        print("="*80)
        
        logger.info("Génération du fichier Excel complet terminée avec succès")
        
    except KeyboardInterrupt:
        logger.info("Processus interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Erreur lors de la génération: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
