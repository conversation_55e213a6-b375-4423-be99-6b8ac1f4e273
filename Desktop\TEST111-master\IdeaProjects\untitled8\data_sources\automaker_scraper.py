"""
Automaker Reports Scraper
Scrapes financial and operational data from major automaker annual reports and investor presentations
"""

import os
import re
import json
import pandas as pd
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging
from datetime import datetime
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhanced_config import AUTOMAKER_CONFIG, REQUEST_CONFIG, DATA_DIR, FILES_DIR
from utils import make_request, clean_numeric_value, sanitize_filename, get_file_name_from_url

class AutomakerScraper:
    """Scraper for automaker financial and operational data"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data = []
        self.download_links = []
        self.companies_data = {}
        
    def scrape_all_automakers(self, years=None):
        """Scrape data from all configured automakers"""
        if years is None:
            years = [datetime.now().year - i for i in range(3)]  # Last 3 years
            
        self.logger.info(f"Scraping automaker data for years: {years}")
        
        for company_key, company_config in AUTOMAKER_CONFIG['companies'].items():
            try:
                self.logger.info(f"Scraping data for {company_config['name']}")
                self._scrape_company_data(company_key, company_config, years)
            except Exception as e:
                self.logger.error(f"Failed to scrape data for {company_config['name']}: {e}")
        
        return self.data
    
    def _scrape_company_data(self, company_key, company_config, years):
        """Scrape data for a specific company"""
        # Scrape annual reports
        self._scrape_annual_reports(company_key, company_config, years)
        
        # Scrape quarterly reports
        self._scrape_quarterly_reports(company_key, company_config, years)
        
        # Scrape investor presentations
        self._scrape_investor_presentations(company_key, company_config, years)
    
    def _scrape_annual_reports(self, company_key, company_config, years):
        """Scrape annual reports for a company"""
        try:
            base_url = company_config['investor_url']
            reports_url = base_url + company_config.get('annual_reports_section', '')
            
            response = make_request(reports_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find annual report links
            self._find_report_links(soup, company_key, company_config, years, 'annual')
            
            # Extract financial data from page content
            self._extract_financial_data(soup, company_key, 'annual')
            
        except Exception as e:
            self.logger.error(f"Error scraping annual reports for {company_key}: {e}")
    
    def _scrape_quarterly_reports(self, company_key, company_config, years):
        """Scrape quarterly reports for a company"""
        try:
            base_url = company_config['investor_url']
            reports_url = base_url + company_config.get('quarterly_reports_section', '')
            
            response = make_request(reports_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find quarterly report links
            self._find_report_links(soup, company_key, company_config, years, 'quarterly')
            
            # Extract financial data from page content
            self._extract_financial_data(soup, company_key, 'quarterly')
            
        except Exception as e:
            self.logger.error(f"Error scraping quarterly reports for {company_key}: {e}")
    
    def _scrape_investor_presentations(self, company_key, company_config, years):
        """Scrape investor presentations for a company"""
        try:
            base_url = company_config['investor_url']
            
            response = make_request(base_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find presentation links
            self._find_presentation_links(soup, company_key, company_config, years)
            
        except Exception as e:
            self.logger.error(f"Error scraping investor presentations for {company_key}: {e}")
    
    def _find_report_links(self, soup, company_key, company_config, years, report_type):
        """Find links to downloadable reports"""
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link['href']
            text = link.get_text(strip=True).lower()
            
            # Check if link matches search terms and years
            if any(term in text for term in company_config['search_terms']):
                for year in years:
                    if str(year) in text or str(year) in href:
                        # Check if it's a downloadable file
                        if any(ext in href.lower() for ext in ['.pdf', '.xlsx', '.xls']):
                            full_url = urljoin(company_config['investor_url'], href)
                            filename = get_file_name_from_url(full_url)
                            
                            self.download_links.append({
                                'url': full_url,
                                'filename': sanitize_filename(f"{company_key}_{year}_{report_type}_{filename}"),
                                'company': company_key,
                                'year': year,
                                'type': report_type,
                                'description': link.get_text(strip=True),
                                'source': 'automaker_report'
                            })
    
    def _find_presentation_links(self, soup, company_key, company_config, years):
        """Find links to investor presentations"""
        # Look for presentation-related links
        presentation_keywords = ['presentation', 'investor', 'earnings', 'results', 'outlook']
        
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link['href']
            text = link.get_text(strip=True).lower()
            
            if any(keyword in text for keyword in presentation_keywords):
                for year in years:
                    if str(year) in text or str(year) in href:
                        if any(ext in href.lower() for ext in ['.pdf', '.pptx', '.ppt']):
                            full_url = urljoin(company_config['investor_url'], href)
                            filename = get_file_name_from_url(full_url)
                            
                            self.download_links.append({
                                'url': full_url,
                                'filename': sanitize_filename(f"{company_key}_{year}_presentation_{filename}"),
                                'company': company_key,
                                'year': year,
                                'type': 'presentation',
                                'description': link.get_text(strip=True),
                                'source': 'automaker_presentation'
                            })
    
    def _extract_financial_data(self, soup, company_key, report_type):
        """Extract financial data from page content"""
        # Look for financial tables
        tables = soup.find_all('table')
        
        for table in tables:
            self._parse_financial_table(table, company_key, report_type)
        
        # Look for key financial metrics in text
        self._extract_text_metrics(soup, company_key, report_type)
    
    def _parse_financial_table(self, table, company_key, report_type):
        """Parse financial data from HTML table"""
        try:
            rows = table.find_all('tr')
            headers = []
            
            # Extract headers
            if rows:
                header_row = rows[0]
                headers = [th.get_text(strip=True).lower() for th in header_row.find_all(['th', 'td'])]
            
            # Extract data rows
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    row_data = {
                        'company': company_key,
                        'report_type': report_type,
                        'source': 'automaker_financial'
                    }
                    
                    for i, cell in enumerate(cells):
                        if i < len(headers):
                            header = headers[i]
                            value = cell.get_text(strip=True)
                            
                            # Categorize financial metrics
                            if 'year' in header:
                                row_data['year'] = clean_numeric_value(value)
                            elif any(term in header for term in ['revenue', 'sales', 'income']):
                                row_data['metric'] = header
                                row_data['value'] = self._parse_financial_value(value)
                                row_data['unit'] = self._detect_currency_unit(value)
                            elif any(term in header for term in ['production', 'volume', 'units']):
                                row_data['metric'] = header
                                row_data['value'] = clean_numeric_value(value)
                                row_data['unit'] = 'units'
                    
                    if 'metric' in row_data and 'value' in row_data and row_data['value'] is not None:
                        self.data.append(row_data)
                        
        except Exception as e:
            self.logger.error(f"Error parsing financial table for {company_key}: {e}")
    
    def _extract_text_metrics(self, soup, company_key, report_type):
        """Extract key financial metrics from text content"""
        text_content = soup.get_text()
        
        # Patterns for financial metrics
        patterns = {
            'revenue': r'revenue.*?(\$[\d,]+\.?\d*\s*(?:billion|million))',
            'net_income': r'net income.*?(\$[\d,]+\.?\d*\s*(?:billion|million))',
            'production': r'production.*?(\d{1,3}(?:,\d{3})*)\s*(?:vehicles|units)',
            'sales_volume': r'sales.*?(\d{1,3}(?:,\d{3})*)\s*(?:vehicles|units)',
            'market_share': r'market share.*?(\d+\.?\d*%)',
            'electric_vehicles': r'electric.*?(\d{1,3}(?:,\d{3})*)\s*(?:vehicles|units)'
        }
        
        for metric_name, pattern in patterns.items():
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            
            for match in matches:
                try:
                    if metric_name in ['revenue', 'net_income']:
                        value = self._parse_financial_value(match)
                        unit = 'USD'
                    elif metric_name == 'market_share':
                        value = float(match.replace('%', ''))
                        unit = 'percentage'
                    else:
                        value = clean_numeric_value(match)
                        unit = 'units'
                    
                    if value is not None:
                        self.data.append({
                            'company': company_key,
                            'metric': metric_name,
                            'value': value,
                            'unit': unit,
                            'year': datetime.now().year,  # Default to current year
                            'report_type': report_type,
                            'source': 'automaker_text_extraction'
                        })
                        
                except Exception as e:
                    self.logger.error(f"Error parsing metric {metric_name} for {company_key}: {e}")
    
    def _parse_financial_value(self, value_str):
        """Parse financial value from string like '$5.2 billion'"""
        try:
            # Remove $ and extract number
            number_str = re.sub(r'[^\d.]', '', value_str.split()[0])
            number = float(number_str)
            
            # Apply multiplier
            if 'billion' in value_str.lower():
                number *= 1_000_000_000
            elif 'million' in value_str.lower():
                number *= 1_000_000
                
            return number
        except:
            return None
    
    def _detect_currency_unit(self, value_str):
        """Detect currency unit from value string"""
        if '$' in value_str:
            return 'USD'
        elif '€' in value_str:
            return 'EUR'
        elif '¥' in value_str:
            return 'JPY'
        else:
            return 'USD'  # Default
    
    def download_reports(self):
        """Download found reports and presentations"""
        if not self.download_links:
            self.logger.warning("No download links found")
            return []
        
        downloaded_files = []
        
        for link_info in self.download_links:
            try:
                response = make_request(link_info['url'])
                
                filepath = os.path.join(FILES_DIR, link_info['filename'])
                
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                self.logger.info(f"Downloaded: {link_info['filename']}")
                downloaded_files.append(filepath)
                
            except Exception as e:
                self.logger.error(f"Failed to download {link_info['filename']}: {e}")
        
        return downloaded_files
    
    def get_company_summary(self, company_key):
        """Get summary for specific company"""
        company_data = [d for d in self.data if d.get('company') == company_key]
        
        if not company_data:
            return f"No data available for {company_key}"
        
        df = pd.DataFrame(company_data)
        
        summary = {
            'company': company_key,
            'total_records': len(df),
            'metrics_available': df['metric'].unique().tolist() if 'metric' in df.columns else [],
            'years_covered': sorted(df['year'].unique().tolist()) if 'year' in df.columns else [],
            'report_types': df['report_type'].unique().tolist() if 'report_type' in df.columns else [],
            'download_links': len([l for l in self.download_links if l.get('company') == company_key])
        }
        
        return summary
    
    def save_data(self, filename=None):
        """Save automaker data to file"""
        if not self.data:
            self.logger.warning("No automaker data to save")
            return None
            
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"automaker_data_{timestamp}.json"
        
        filepath = os.path.join(DATA_DIR, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"Automaker data saved to: {filepath}")
        return filepath
    
    def get_summary(self):
        """Get summary of all scraped automaker data"""
        if not self.data:
            return "No automaker data available"
        
        df = pd.DataFrame(self.data)
        
        summary = {
            'source': 'Automaker Reports',
            'total_records': len(df),
            'companies_covered': df['company'].unique().tolist() if 'company' in df.columns else [],
            'metrics_available': df['metric'].unique().tolist() if 'metric' in df.columns else [],
            'years_covered': sorted(df['year'].unique().tolist()) if 'year' in df.columns else [],
            'report_types': df['report_type'].unique().tolist() if 'report_type' in df.columns else [],
            'total_download_links': len(self.download_links)
        }
        
        return summary
