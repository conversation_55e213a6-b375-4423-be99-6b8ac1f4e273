"""
Excel Generator for Automotive Data
Creates comprehensive Excel reports with multiple sheets and formatting
"""

import os
import pandas as pd
import xlsxwriter
from datetime import datetime
import logging
import numpy as np

from enhanced_config import EXCEL_CONFIG, EXCEL_DIR

class AutomotiveExcelGenerator:
    """Generates comprehensive Excel reports from consolidated automotive data"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.workbook = None
        self.worksheet_formats = {}
        
    def create_comprehensive_report(self, consolidated_data, unified_datasets, summary_data):
        """Create comprehensive Excel report with all data"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = EXCEL_CONFIG['filename_template'].format(timestamp=timestamp)
        filepath = os.path.join(EXCEL_DIR, filename)
        
        self.logger.info(f"Creating comprehensive Excel report: {filename}")
        
        # Create workbook
        self.workbook = xlsxwriter.Workbook(filepath)
        self._create_formats()
        
        try:
            # Create all worksheets
            self._create_executive_summary(summary_data)
            self._create_oica_production_sheet(unified_datasets.get('production'))
            self._create_trade_data_sheet(unified_datasets.get('trade'))
            self._create_materials_pricing_sheet(unified_datasets.get('materials'))
            self._create_automaker_financial_sheet(unified_datasets.get('financial'))
            self._create_market_analysis_sheet(unified_datasets.get('market_analysis'))
            self._create_trends_analysis_sheet(unified_datasets)
            self._create_data_quality_sheet(summary_data.get('data_quality', {}))
            
            # Close workbook
            self.workbook.close()
            
            self.logger.info(f"Excel report created successfully: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error creating Excel report: {e}")
            if self.workbook:
                self.workbook.close()
            raise
    
    def _create_formats(self):
        """Create formatting styles for the workbook"""
        # Header format
        self.worksheet_formats['header'] = self.workbook.add_format({
            'bold': True,
            'bg_color': EXCEL_CONFIG['formatting']['header_style']['bg_color'],
            'font_color': EXCEL_CONFIG['formatting']['header_style']['font_color'],
            'border': EXCEL_CONFIG['formatting']['header_style']['border'],
            'align': 'center',
            'valign': 'vcenter'
        })
        
        # Data format
        self.worksheet_formats['data'] = self.workbook.add_format({
            'border': EXCEL_CONFIG['formatting']['data_style']['border'],
            'align': EXCEL_CONFIG['formatting']['data_style']['align']
        })
        
        # Number format
        self.worksheet_formats['number'] = self.workbook.add_format({
            'num_format': EXCEL_CONFIG['formatting']['number_format'],
            'border': 1,
            'align': 'right'
        })
        
        # Percentage format
        self.worksheet_formats['percentage'] = self.workbook.add_format({
            'num_format': EXCEL_CONFIG['formatting']['percentage_format'],
            'border': 1,
            'align': 'right'
        })
        
        # Currency format
        self.worksheet_formats['currency'] = self.workbook.add_format({
            'num_format': EXCEL_CONFIG['formatting']['currency_format'],
            'border': 1,
            'align': 'right'
        })
        
        # Title format
        self.worksheet_formats['title'] = self.workbook.add_format({
            'bold': True,
            'font_size': 16,
            'align': 'center'
        })
        
        # Subtitle format
        self.worksheet_formats['subtitle'] = self.workbook.add_format({
            'bold': True,
            'font_size': 12,
            'bg_color': '#E7E6E6'
        })
    
    def _create_executive_summary(self, summary_data):
        """Create executive summary worksheet"""
        worksheet = self.workbook.add_worksheet(EXCEL_CONFIG['sheets']['summary'])
        
        # Title
        worksheet.merge_range('A1:F1', 'Automotive Industry Data - Executive Summary', 
                            self.worksheet_formats['title'])
        
        # Collection info
        row = 3
        worksheet.write(row, 0, 'Report Generated:', self.worksheet_formats['subtitle'])
        worksheet.write(row, 1, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        row += 2
        worksheet.write(row, 0, 'Data Sources Summary:', self.worksheet_formats['subtitle'])
        
        # Sources summary
        if 'source_summaries' in summary_data:
            row += 1
            headers = ['Source', 'Records', 'Years Covered', 'Status']
            for col, header in enumerate(headers):
                worksheet.write(row, col, header, self.worksheet_formats['header'])
            
            row += 1
            for source, data in summary_data['source_summaries'].items():
                worksheet.write(row, 0, source.upper(), self.worksheet_formats['data'])
                worksheet.write(row, 1, data.get('total_records', 0), self.worksheet_formats['number'])
                
                years = data.get('years_covered', [])
                years_str = f"{min(years)}-{max(years)}" if years else "N/A"
                worksheet.write(row, 2, years_str, self.worksheet_formats['data'])
                worksheet.write(row, 3, 'OK', self.worksheet_formats['data'])
                row += 1
        
        # Key metrics
        row += 2
        worksheet.write(row, 0, 'Key Global Metrics (Latest Year):', self.worksheet_formats['subtitle'])
        
        # Auto-adjust column widths
        worksheet.set_column('A:A', 25)
        worksheet.set_column('B:F', 15)
    
    def _create_oica_production_sheet(self, production_df):
        """Create OICA production data sheet"""
        if production_df is None or production_df.empty:
            return
        
        worksheet = self.workbook.add_worksheet(EXCEL_CONFIG['sheets']['oica_production'])
        
        # Title
        worksheet.merge_range('A1:G1', 'Global Vehicle Production Statistics (OICA)', 
                            self.worksheet_formats['title'])
        
        # Write data
        self._write_dataframe_to_worksheet(worksheet, production_df, start_row=3)
        
        # Auto-adjust columns
        worksheet.set_column('A:A', 20)  # Country
        worksheet.set_column('B:G', 15)  # Data columns
    
    def _create_trade_data_sheet(self, trade_df):
        """Create trade data sheet"""
        if trade_df is None or trade_df.empty:
            return
        
        worksheet = self.workbook.add_worksheet(EXCEL_CONFIG['sheets']['us_trade'])
        
        # Title
        worksheet.merge_range('A1:F1', 'Automotive Trade Statistics', 
                            self.worksheet_formats['title'])
        
        # Write data
        self._write_dataframe_to_worksheet(worksheet, trade_df, start_row=3)
        
        # Auto-adjust columns
        worksheet.set_column('A:F', 15)
    
    def _create_materials_pricing_sheet(self, materials_df):
        """Create materials pricing sheet"""
        if materials_df is None or materials_df.empty:
            return
        
        worksheet = self.workbook.add_worksheet(EXCEL_CONFIG['sheets']['materials_pricing'])
        
        # Title
        worksheet.merge_range('A1:H1', 'Raw Materials Pricing Data', 
                            self.worksheet_formats['title'])
        
        # Write data
        self._write_dataframe_to_worksheet(worksheet, materials_df, start_row=3)
        
        # Auto-adjust columns
        worksheet.set_column('A:H', 15)
    
    def _create_automaker_financial_sheet(self, financial_df):
        """Create automaker financial data sheet"""
        if financial_df is None or financial_df.empty:
            return
        
        worksheet = self.workbook.add_worksheet(EXCEL_CONFIG['sheets']['automaker_reports'])
        
        # Title
        worksheet.merge_range('A1:F1', 'Automaker Financial & Operational Data', 
                            self.worksheet_formats['title'])
        
        # Write data
        self._write_dataframe_to_worksheet(worksheet, financial_df, start_row=3)
        
        # Auto-adjust columns
        worksheet.set_column('A:F', 18)
    
    def _create_market_analysis_sheet(self, market_df):
        """Create market analysis sheet"""
        if market_df is None or market_df.empty:
            return
        
        worksheet = self.workbook.add_worksheet(EXCEL_CONFIG['sheets']['market_analysis'])
        
        # Title
        worksheet.merge_range('A1:F1', 'Market Analysis & Cross-Source Data', 
                            self.worksheet_formats['title'])
        
        # Write data
        self._write_dataframe_to_worksheet(worksheet, market_df, start_row=3)
        
        # Auto-adjust columns
        worksheet.set_column('A:F', 15)
    
    def _create_trends_analysis_sheet(self, unified_datasets):
        """Create trends analysis sheet with calculations"""
        worksheet = self.workbook.add_worksheet(EXCEL_CONFIG['sheets']['trends'])
        
        # Title
        worksheet.merge_range('A1:F1', 'Industry Trends & Analysis', 
                            self.worksheet_formats['title'])
        
        row = 3
        
        # Production trends
        if 'production' in unified_datasets and not unified_datasets['production'].empty:
            production_df = unified_datasets['production']
            
            worksheet.write(row, 0, 'Global Production Trends:', self.worksheet_formats['subtitle'])
            row += 2
            
            # Calculate year-over-year changes
            if 'year' in production_df.columns and 'total_production' in production_df.columns:
                yearly_totals = production_df.groupby('year')['total_production'].sum().reset_index()
                yearly_totals['yoy_change'] = yearly_totals['total_production'].pct_change() * 100
                
                # Write yearly trends
                headers = ['Year', 'Global Production', 'YoY Change %']
                for col, header in enumerate(headers):
                    worksheet.write(row, col, header, self.worksheet_formats['header'])
                
                row += 1
                for _, year_data in yearly_totals.iterrows():
                    worksheet.write(row, 0, int(year_data['year']), self.worksheet_formats['data'])
                    worksheet.write(row, 1, year_data['total_production'], self.worksheet_formats['number'])
                    if not pd.isna(year_data['yoy_change']):
                        worksheet.write(row, 2, year_data['yoy_change'], self.worksheet_formats['percentage'])
                    row += 1
        
        # Auto-adjust columns
        worksheet.set_column('A:F', 15)
    
    def _create_data_quality_sheet(self, quality_data):
        """Create data quality assessment sheet"""
        worksheet = self.workbook.add_worksheet('Data Quality')
        
        # Title
        worksheet.merge_range('A1:D1', 'Data Quality Assessment', 
                            self.worksheet_formats['title'])
        
        row = 3
        worksheet.write(row, 0, 'Source', self.worksheet_formats['header'])
        worksheet.write(row, 1, 'Status', self.worksheet_formats['header'])
        worksheet.write(row, 2, 'Records', self.worksheet_formats['header'])
        worksheet.write(row, 3, 'Issues', self.worksheet_formats['header'])
        
        row += 1
        for source, quality_info in quality_data.items():
            worksheet.write(row, 0, source.upper(), self.worksheet_formats['data'])
            worksheet.write(row, 1, quality_info.get('status', 'Unknown'), self.worksheet_formats['data'])
            worksheet.write(row, 2, quality_info.get('records', 0), self.worksheet_formats['number'])
            
            issues = quality_info.get('issues', [])
            issues_text = '; '.join(issues) if issues else 'None'
            worksheet.write(row, 3, issues_text, self.worksheet_formats['data'])
            row += 1
        
        # Auto-adjust columns
        worksheet.set_column('A:A', 15)
        worksheet.set_column('B:B', 12)
        worksheet.set_column('C:C', 10)
        worksheet.set_column('D:D', 40)
    
    def _write_dataframe_to_worksheet(self, worksheet, df, start_row=0):
        """Write pandas DataFrame to worksheet with formatting"""
        if df.empty:
            return
        
        # Write headers
        for col, header in enumerate(df.columns):
            worksheet.write(start_row, col, header, self.worksheet_formats['header'])
        
        # Write data
        for row_idx, (_, row) in enumerate(df.iterrows()):
            for col_idx, value in enumerate(row):
                cell_row = start_row + row_idx + 1
                
                # Choose format based on data type
                if pd.isna(value):
                    worksheet.write(cell_row, col_idx, '', self.worksheet_formats['data'])
                elif isinstance(value, (int, float)):
                    # Check if it's a percentage column
                    if any(term in df.columns[col_idx].lower() for term in ['percent', 'share', '%']):
                        worksheet.write(cell_row, col_idx, value/100 if value > 1 else value, 
                                      self.worksheet_formats['percentage'])
                    # Check if it's a currency column
                    elif any(term in df.columns[col_idx].lower() for term in ['usd', 'revenue', 'income', 'value']):
                        worksheet.write(cell_row, col_idx, value, self.worksheet_formats['currency'])
                    else:
                        worksheet.write(cell_row, col_idx, value, self.worksheet_formats['number'])
                else:
                    worksheet.write(cell_row, col_idx, str(value), self.worksheet_formats['data'])
    
    def create_simple_report(self, data_dict, filename=None):
        """Create a simple Excel report from data dictionary"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"automotive_data_simple_{timestamp}.xlsx"
        
        filepath = os.path.join(EXCEL_DIR, filename)
        
        with pd.ExcelWriter(filepath, engine='xlsxwriter') as writer:
            for sheet_name, data in data_dict.items():
                if isinstance(data, pd.DataFrame) and not data.empty:
                    data.to_excel(writer, sheet_name=sheet_name, index=False)
                elif isinstance(data, list) and data:
                    df = pd.DataFrame(data)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        self.logger.info(f"Simple Excel report created: {filepath}")
        return filepath
