"""
Enhanced utility functions for multi-source automotive data scraper
"""

import os
import re
import time
import requests
from urllib.parse import urljoin, urlparse
import logging
from enhanced_config import REQUEST_CONFIG


def create_directories():
    """Create necessary output directories"""
    from enhanced_config import OUTPUT_DIR, DATA_DIR, FILES_DIR, LOGS_DIR, EXCEL_DIR

    for directory in [OUTPUT_DIR, DATA_DIR, FILES_DIR, LOGS_DIR, EXCEL_DIR]:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")


def clean_numeric_value(value):
    """Clean and convert numeric values from scraped data"""
    if not value or value == '-':
        return None
    
    # Remove spaces and commas
    cleaned = str(value).replace(' ', '').replace(',', '')
    
    # Handle percentage values
    if '%' in cleaned:
        try:
            return float(cleaned.replace('%', ''))
        except ValueError:
            return None
    
    # Handle regular numbers
    try:
        return int(cleaned)
    except ValueError:
        try:
            return float(cleaned)
        except ValueError:
            return None


def make_request(url, retries=None):
    """Enhanced HTTP request with retry logic and better error handling"""
    if retries is None:
        retries = REQUEST_CONFIG['max_retries']

    logger = logging.getLogger(__name__)

    for attempt in range(retries):
        try:
            logger.debug(f"Requesting: {url} (attempt {attempt + 1})")
            response = requests.get(
                url,
                headers=REQUEST_CONFIG['headers'],
                timeout=REQUEST_CONFIG['timeout']
            )
            response.raise_for_status()

            # Add delay between requests
            if REQUEST_CONFIG['delay'] > 0:
                time.sleep(REQUEST_CONFIG['delay'])

            return response

        except requests.exceptions.RequestException as e:
            logger.warning(f"Request failed (attempt {attempt + 1}): {e}")
            if attempt == retries - 1:
                logger.error(f"All {retries} attempts failed for {url}")
                raise
            time.sleep(2 ** attempt)  # Exponential backoff


def get_file_name_from_url(url):
    """Extract filename from URL"""
    parsed_url = urlparse(url)
    filename = os.path.basename(parsed_url.path)
    
    if not filename:
        # Generate filename from URL
        filename = url.split('/')[-1]
        if '?' in filename:
            filename = filename.split('?')[0]
    
    return filename


def sanitize_filename(filename):
    """Sanitize filename for safe saving"""
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    filename = filename.strip()
    
    # Ensure filename is not empty
    if not filename:
        filename = "unknown_file"
    
    return filename


def build_year_url(year):
    """Build URL for specific year statistics"""
    from enhanced_config import OICA_CONFIG
    return f"{OICA_CONFIG['production_stats_base']}/{year}-statistics/"


def is_valid_year(year):
    """Check if year is valid for scraping"""
    from enhanced_config import OICA_CONFIG
    return year in OICA_CONFIG['available_years']


def setup_logging():
    """Setup enhanced logging configuration"""
    from enhanced_config import LOGGING_CONFIG
    import logging.handlers

    logger = logging.getLogger()
    logger.setLevel(getattr(logging, LOGGING_CONFIG['level']))

    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        LOGGING_CONFIG['file'],
        maxBytes=LOGGING_CONFIG['max_bytes'],
        backupCount=LOGGING_CONFIG['backup_count']
    )
    file_handler.setFormatter(logging.Formatter(LOGGING_CONFIG['format']))

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(LOGGING_CONFIG['format']))

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


def validate_file_size(filepath, max_size=None):
    """Validate file size before processing"""
    if max_size is None:
        from enhanced_config import FILE_CONFIG
        max_size = FILE_CONFIG['max_file_size']

    if os.path.exists(filepath):
        file_size = os.path.getsize(filepath)
        return file_size <= max_size
    return False


def detect_file_encoding(filepath):
    """Detect file encoding for better text processing"""
    import chardet

    try:
        with open(filepath, 'rb') as f:
            raw_data = f.read(10000)  # Read first 10KB
            result = chardet.detect(raw_data)
            return result.get('encoding', 'utf-8')
    except:
        return 'utf-8'


def safe_filename(filename, max_length=255):
    """Create safe filename with length limit"""
    # Remove invalid characters
    safe_name = sanitize_filename(filename)

    # Truncate if too long
    if len(safe_name) > max_length:
        name, ext = os.path.splitext(safe_name)
        max_name_length = max_length - len(ext)
        safe_name = name[:max_name_length] + ext

    return safe_name


def format_number(value, format_type='number'):
    """Format numbers for display"""
    if value is None:
        return 'N/A'

    try:
        if format_type == 'currency':
            return f"${value:,.2f}"
        elif format_type == 'percentage':
            return f"{value:.2f}%"
        elif format_type == 'thousands':
            return f"{value:,.0f}"
        else:
            return f"{value:,}"
    except:
        return str(value)


def calculate_percentage_change(current, previous):
    """Calculate percentage change between two values"""
    if previous == 0 or previous is None or current is None:
        return None

    try:
        return ((current - previous) / previous) * 100
    except:
        return None
