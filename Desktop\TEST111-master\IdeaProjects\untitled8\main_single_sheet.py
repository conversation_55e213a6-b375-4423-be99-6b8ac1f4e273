"""
Script pour générer UN SEUL ONGLET Excel avec TOUTES les données combinées
Toutes les données dans une seule page Excel
"""

import argparse
import sys
import logging
from datetime import datetime
import pandas as pd
import numpy as np
import os

# Setup logging
from enhanced_config import LOGGING_CONFIG, EXCEL_DIR

logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG['file']),
        logging.StreamHandler(sys.stdout)
    ]
)

from project_scraper import ProjectDataCollector
from unified_data_generator import UnifiedAutomotiveDataGenerator

class SingleSheetExcelGenerator:
    """Générateur d'un fichier Excel avec UNE SEULE page contenant toutes les données"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def create_single_sheet_excel(self, years=None):
        """Crée un fichier Excel avec une seule page contenant toutes les données"""
        if years is None:
            years = [2023, 2024]
        
        self.logger.info("Création d'un fichier Excel avec UNE SEULE page")
        
        # 1. Collecter toutes les données
        self.logger.info("Collecte de toutes les données...")
        project_collector = ProjectDataCollector()
        project_data = project_collector.collect_all_project_data()
        
        # 2. Créer le DataFrame unifié principal
        self.logger.info("Création du DataFrame unifié...")
        unified_df = self._create_master_dataframe(project_data, years)
        
        # 3. Sauvegarder en Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"automotive_single_sheet_{timestamp}.xlsx"
        filepath = os.path.join(EXCEL_DIR, filename)
        
        self.logger.info(f"Sauvegarde en Excel: {filename}")
        
        # Créer Excel avec formatage
        with pd.ExcelWriter(filepath, engine='xlsxwriter') as writer:
            unified_df.to_excel(writer, sheet_name='Automotive_Data_Complete', index=False)
            
            # Formatage
            workbook = writer.book
            worksheet = writer.sheets['Automotive_Data_Complete']
            
            # Formats
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4472C4',
                'font_color': 'white',
                'border': 1,
                'align': 'center'
            })
            
            number_format = workbook.add_format({
                'num_format': '#,##0',
                'border': 1,
                'align': 'right'
            })
            
            percentage_format = workbook.add_format({
                'num_format': '0.0%',
                'border': 1,
                'align': 'right'
            })
            
            currency_format = workbook.add_format({
                'num_format': '$#,##0',
                'border': 1,
                'align': 'right'
            })
            
            text_format = workbook.add_format({
                'border': 1,
                'align': 'center'
            })
            
            # Appliquer le formatage des headers
            for col_num, column in enumerate(unified_df.columns):
                worksheet.write(0, col_num, column, header_format)
                
                # Auto-ajuster la largeur des colonnes
                max_length = max(len(str(column)), 15)
                worksheet.set_column(col_num, col_num, max_length)
            
            # Appliquer le formatage des données
            for row_num in range(len(unified_df)):
                for col_num, column in enumerate(unified_df.columns):
                    value = unified_df.iloc[row_num, col_num]
                    cell_row = row_num + 1
                    
                    if pd.isna(value):
                        worksheet.write(cell_row, col_num, '', text_format)
                    elif 'percentage' in column.lower() or 'share' in column.lower() or 'tariff' in column.lower():
                        if isinstance(value, (int, float)):
                            worksheet.write(cell_row, col_num, value, percentage_format)
                        else:
                            worksheet.write(cell_row, col_num, value, text_format)
                    elif 'price' in column.lower() or 'revenue' in column.lower() or 'income' in column.lower():
                        if isinstance(value, (int, float)):
                            worksheet.write(cell_row, col_num, value, currency_format)
                        else:
                            worksheet.write(cell_row, col_num, value, text_format)
                    elif 'volume' in column.lower() or 'production' in column.lower() or 'sales' in column.lower():
                        if isinstance(value, (int, float)):
                            worksheet.write(cell_row, col_num, value, number_format)
                        else:
                            worksheet.write(cell_row, col_num, value, text_format)
                    else:
                        worksheet.write(cell_row, col_num, value, text_format)
        
        self.logger.info(f"Fichier Excel créé: {filepath}")
        return filepath, len(unified_df)
    
    def _create_master_dataframe(self, project_data, years):
        """Crée un DataFrame maître avec toutes les données combinées"""
        all_records = []
        
        # 1. Données de production historique (base principale)
        production_data = project_data.get('production_historical', [])
        self.logger.info(f"Traitement de {len(production_data)} enregistrements de production")
        
        for record in production_data:
            # Créer un enregistrement de base
            base_record = {
                'Year': record.get('year'),
                'Country': record.get('country_region', ''),
                'Manufacturer': 'Multiple',  # Sera détaillé plus tard
                'VehicleType': 'Total',
                'ProductionVolume': record.get('total', 0),
                'PassengerCars': record.get('cars', 0),
                'CommercialVehicles': record.get('commercial_vehicles', 0),
                'TariffRate_USA': self._get_tariff_rate(record.get('country_region', '')),
                'SteelPrice': self._get_steel_price(record.get('year')),
                'EV_Share': self._get_country_ev_share(record.get('country_region', ''), record.get('year')),
                'ImportVolume_USA': self._estimate_import_volume(record.get('country_region', ''), record.get('total', 0)),
                'DataSource': 'OICA_Production',
                'DataCategory': 'Production_Historical'
            }
            all_records.append(base_record)
        
        # 2. Ajouter les données de matières premières
        materials_data = project_data.get('raw_materials', [])
        self.logger.info(f"Traitement de {len(materials_data)} points de matières premières")
        
        # Grouper par année et matériau pour éviter trop de lignes
        materials_df = pd.DataFrame(materials_data)
        if not materials_df.empty:
            materials_df['date'] = pd.to_datetime(materials_df['date'])
            materials_df['year'] = materials_df['date'].dt.year
            
            # Moyenne annuelle par matériau
            materials_yearly = materials_df.groupby(['year', 'material'])['price'].mean().reset_index()
            
            for _, row in materials_yearly.iterrows():
                material_record = {
                    'Year': row['year'],
                    'Country': 'Global',
                    'Manufacturer': 'N/A',
                    'VehicleType': 'Raw_Material',
                    'ProductionVolume': 0,
                    'PassengerCars': 0,
                    'CommercialVehicles': 0,
                    'TariffRate_USA': 0,
                    'SteelPrice': row['price'] if row['material'] == 'Steel' else 0,
                    'AluminumPrice': row['price'] if row['material'] == 'Aluminum' else 0,
                    'LithiumPrice': row['price'] if row['material'] == 'Lithium' else 0,
                    'CopperPrice': row['price'] if row['material'] == 'Copper' else 0,
                    'MaterialType': row['material'],
                    'MaterialPrice': row['price'],
                    'EV_Share': 0,
                    'ImportVolume_USA': 0,
                    'DataSource': 'Materials_Pricing',
                    'DataCategory': 'Raw_Materials'
                }
                all_records.append(material_record)
        
        # 3. Ajouter les données EV
        ev_data = project_data.get('ev_transition', [])
        self.logger.info(f"Traitement de {len(ev_data)} enregistrements EV")
        
        for record in ev_data:
            if 'manufacturer' in record:  # Données de ventes EV
                ev_record = {
                    'Year': record.get('year'),
                    'Country': record.get('region', 'Global'),
                    'Manufacturer': record.get('manufacturer'),
                    'VehicleType': 'Electric',
                    'ProductionVolume': record.get('ev_sales_units', 0),
                    'PassengerCars': record.get('ev_sales_units', 0),
                    'CommercialVehicles': 0,
                    'TariffRate_USA': self._get_tariff_rate(record.get('region', '')),
                    'SteelPrice': self._get_steel_price(record.get('year')),
                    'EV_Share': 1.0,  # 100% pour les véhicules électriques
                    'EV_Sales_Units': record.get('ev_sales_units', 0),
                    'ImportVolume_USA': 0,
                    'DataSource': 'EV_Sales',
                    'DataCategory': 'EV_Transition'
                }
                all_records.append(ev_record)
            
            elif 'region' in record and 'ev_market_share_percent' in record:  # Parts de marché EV
                market_record = {
                    'Year': record.get('year'),
                    'Country': record.get('region'),
                    'Manufacturer': 'Market_Average',
                    'VehicleType': 'Market_Share',
                    'ProductionVolume': 0,
                    'PassengerCars': 0,
                    'CommercialVehicles': 0,
                    'TariffRate_USA': self._get_tariff_rate(record.get('region', '')),
                    'SteelPrice': self._get_steel_price(record.get('year')),
                    'EV_Share': record.get('ev_market_share_percent', 0) / 100,
                    'EV_Market_Share_Percent': record.get('ev_market_share_percent', 0),
                    'ImportVolume_USA': 0,
                    'DataSource': 'EV_Market_Share',
                    'DataCategory': 'EV_Transition'
                }
                all_records.append(market_record)
        
        # 4. Ajouter les données financières des constructeurs
        financial_data = project_data.get('manufacturer_financials', [])
        self.logger.info(f"Traitement de {len(financial_data)} enregistrements financiers")
        
        # Grouper par constructeur et année
        financial_df = pd.DataFrame(financial_data)
        if not financial_df.empty:
            for (manufacturer, year), group in financial_df.groupby(['manufacturer', 'year']):
                financial_record = {
                    'Year': year,
                    'Country': self._get_manufacturer_country(manufacturer),
                    'Manufacturer': manufacturer,
                    'VehicleType': 'Financial_Data',
                    'ProductionVolume': 0,
                    'PassengerCars': 0,
                    'CommercialVehicles': 0,
                    'TariffRate_USA': self._get_tariff_rate(self._get_manufacturer_country(manufacturer)),
                    'SteelPrice': self._get_steel_price(year),
                    'EV_Share': 0,
                    'ImportVolume_USA': 0,
                    'DataSource': 'Financial_Reports',
                    'DataCategory': 'Financial_Data'
                }
                
                # Ajouter les métriques financières
                for _, row in group.iterrows():
                    metric = row['metric']
                    value = row['value']
                    
                    if metric == 'revenue':
                        financial_record['Revenue_USD'] = value
                    elif metric == 'net_income':
                        financial_record['NetIncome_USD'] = value
                    elif metric == 'rd_spending':
                        financial_record['RD_Spending_USD'] = value
                    elif metric == 'capex':
                        financial_record['CapEx_USD'] = value
                
                all_records.append(financial_record)
        
        # 5. Ajouter les données commerciales
        trade_data = project_data.get('trade_data', {})
        if trade_data.get('trade_flows'):
            self.logger.info(f"Traitement des données commerciales")
            
            for record in trade_data['trade_flows']:
                trade_record = {
                    'Year': record.get('year'),
                    'Country': record.get('country'),
                    'Manufacturer': 'Trade_Data',
                    'VehicleType': 'Import_Export',
                    'ProductionVolume': 0,
                    'PassengerCars': 0,
                    'CommercialVehicles': 0,
                    'TariffRate_USA': self._get_tariff_rate(record.get('country', '')),
                    'SteelPrice': self._get_steel_price(record.get('year')),
                    'EV_Share': 0,
                    'ImportVolume_USA': record.get('import_value_usd', 0) / 50000,  # Estimation volume
                    'Import_Value_USD': record.get('import_value_usd', 0),
                    'Trade_Type': record.get('trade_type'),
                    'DataSource': 'Trade_Statistics',
                    'DataCategory': 'Trade_Data'
                }
                all_records.append(trade_record)
        
        # Créer le DataFrame final
        df = pd.DataFrame(all_records)
        
        # Nettoyer et standardiser
        df = self._clean_and_standardize_dataframe(df)
        
        self.logger.info(f"DataFrame final créé avec {len(df)} enregistrements")
        return df
    
    def _clean_and_standardize_dataframe(self, df):
        """Nettoie et standardise le DataFrame"""
        # Remplir les valeurs manquantes
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        df[numeric_columns] = df[numeric_columns].fillna(0)
        
        text_columns = df.select_dtypes(include=['object']).columns
        df[text_columns] = df[text_columns].fillna('')
        
        # Standardiser les noms de pays
        df['Country'] = df['Country'].replace({
            'USA': 'United States',
            'UK': 'United Kingdom',
            'Korea': 'South Korea'
        })
        
        # Trier par année et pays
        df = df.sort_values(['Year', 'Country', 'Manufacturer'])
        
        return df
    
    def _get_tariff_rate(self, country):
        """Obtient le taux de tarif pour un pays"""
        tariff_rates = {
            'China': 0.275,
            'Germany': 0.025,
            'Japan': 0.025,
            'South Korea': 0.025,
            'Mexico': 0.0,
            'Canada': 0.0,
            'United Kingdom': 0.025,
            'Italy': 0.025,
            'France': 0.025
        }
        return tariff_rates.get(country, 0.025)
    
    def _get_steel_price(self, year):
        """Obtient le prix de l'acier pour une année"""
        steel_prices = {
            2010: 580, 2011: 620, 2012: 590, 2013: 560, 2014: 540,
            2015: 480, 2016: 520, 2017: 590, 2018: 650, 2019: 620,
            2020: 550, 2021: 780, 2022: 920, 2023: 720, 2024: 760
        }
        return steel_prices.get(year, 720)
    
    def _get_country_ev_share(self, country, year):
        """Obtient la part EV pour un pays et une année"""
        ev_shares = {
            ('China', 2023): 0.357, ('China', 2024): 0.42,
            ('Germany', 2023): 0.238, ('Germany', 2024): 0.265,
            ('United States', 2023): 0.076, ('United States', 2024): 0.091,
            ('Japan', 2023): 0.039, ('Japan', 2024): 0.048
        }
        return ev_shares.get((country, year), 0.05)
    
    def _estimate_import_volume(self, country, production_volume):
        """Estime le volume d'importation vers les USA"""
        export_rates = {
            'Japan': 0.35,
            'Germany': 0.30,
            'South Korea': 0.25,
            'Mexico': 0.40,
            'Canada': 0.30,
            'China': 0.10
        }
        rate = export_rates.get(country, 0.15)
        return int(production_volume * rate) if production_volume else 0
    
    def _get_manufacturer_country(self, manufacturer):
        """Obtient le pays d'origine d'un constructeur"""
        manufacturer_countries = {
            'Toyota': 'Japan',
            'Volkswagen Group': 'Germany',
            'Ford': 'United States',
            'General Motors': 'United States',
            'Stellantis': 'Italy',
            'Hyundai-Kia': 'South Korea',
            'BMW Group': 'Germany',
            'Mercedes-Benz': 'Germany',
            'Honda': 'Japan',
            'Nissan': 'Japan',
            'BYD': 'China',
            'Tesla': 'United States',
            'SAIC': 'China',
            'Geely': 'China',
            'Great Wall': 'China'
        }
        return manufacturer_countries.get(manufacturer, 'Unknown')

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(
        description='Générateur Excel UNE SEULE PAGE avec toutes les données'
    )
    
    parser.add_argument('--years', type=int, nargs='+', 
                       default=[2023, 2024],
                       help='Années pour les données unifiées')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Mode verbose')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    print("\n" + "="*80)
    print("📊 GÉNÉRATION EXCEL UNE SEULE PAGE - TOUTES DONNÉES COMBINÉES")
    print("="*80)
    print("🎯 Toutes les données dans UNE SEULE feuille Excel")
    print("📋 Combine: Production + Commerce + Matières premières + EV + Financier")
    print("="*80)
    
    try:
        generator = SingleSheetExcelGenerator()
        excel_file, total_records = generator.create_single_sheet_excel(args.years)
        
        print("\n" + "="*80)
        print("✅ FICHIER EXCEL UNE SEULE PAGE CRÉÉ AVEC SUCCÈS!")
        print("="*80)
        print(f"📁 Fichier: {excel_file}")
        print(f"📊 Total enregistrements: {total_records:,}")
        print(f"📋 Format: UNE SEULE page avec toutes les données")
        print(f"🎯 Prêt pour analyse complète!")
        print("="*80)
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
