"""
Enhanced OICA Production Statistics Scraper
Improved version with better error handling, data validation, and multi-year support
"""

import os
import json
import pandas as pd
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging
from datetime import datetime
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhanced_config import OICA_CONFIG, REQUEST_CONFIG, DATA_DIR, FILES_DIR
from utils import make_request, clean_numeric_value, get_file_name_from_url, sanitize_filename

class EnhancedOICAScraper:
    """Enhanced OICA Production Statistics Scraper"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data = []
        self.download_links = []
        self.years_data = {}
        
    def scrape_multiple_years(self, years=None):
        """Scrape production data for multiple years"""
        if years is None:
            years = OICA_CONFIG['available_years'][:5]  # Last 5 years by default
        
        self.logger.info(f"Scraping OICA data for years: {years}")
        
        for year in years:
            try:
                self.logger.info(f"Scraping data for year {year}")
                year_data = self._scrape_year_data(year)
                self.years_data[year] = year_data
                self.data.extend(year_data)
            except Exception as e:
                self.logger.error(f"Failed to scrape data for year {year}: {e}")
        
        return self.data
    
    def _scrape_year_data(self, year):
        """Scrape production data for a specific year"""
        if year not in OICA_CONFIG['available_years']:
            raise ValueError(f"Year {year} is not available for scraping")
        
        url = self._build_year_url(year)
        response = make_request(url)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        year_data = []
        
        # Try multiple methods to extract data
        # Method 1: HTML table
        table = soup.find('table') or self._find_table_alternative(soup)
        if table:
            year_data = self._parse_table(table, year)
        
        # Method 2: Text extraction if no table found
        if not year_data:
            self.logger.info(f"No table found for {year}, trying text extraction")
            year_data = self._extract_from_text(soup, year)
        
        # Method 3: Look for downloadable files with data
        if not year_data:
            self.logger.info(f"No text data found for {year}, looking for downloadable files")
            self._find_download_links(soup, year)
        
        self.logger.info(f"Extracted {len(year_data)} records for year {year}")
        return year_data
    
    def _build_year_url(self, year):
        """Build URL for specific year statistics"""
        return f"{OICA_CONFIG['production_stats_base']}/{year}-statistics/"
    
    def _find_table_alternative(self, soup):
        """Alternative method to find table data"""
        # Look for table-like structures in divs or other elements
        selectors = [
            'table',
            '.table',
            '[class*="table"]',
            '.data-table',
            '.statistics-table',
            '.production-data'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element
        
        return None
    
    def _parse_table(self, table, year):
        """Parse HTML table with enhanced error handling"""
        data = []
        
        try:
            rows = table.find_all('tr')
            headers = []
            
            # Extract headers with multiple fallback methods
            header_row = rows[0] if rows else None
            if header_row:
                headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
            
            # Clean and standardize headers
            headers = self._standardize_headers(headers)
            
            # Extract data rows
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 3:  # Minimum expected columns
                    row_data = self._parse_table_row(cells, headers, year)
                    if row_data and self._validate_row_data(row_data):
                        data.append(row_data)
            
            self.logger.info(f"Successfully parsed table with {len(data)} rows for year {year}")
            
        except Exception as e:
            self.logger.error(f"Error parsing table for year {year}: {e}")
        
        return data
    
    def _standardize_headers(self, headers):
        """Standardize header names for consistency"""
        standardized = []
        
        for header in headers:
            header_lower = header.lower().strip()
            
            # Map common variations to standard names
            if any(term in header_lower for term in ['country', 'region', 'nation']):
                standardized.append('country_region')
            elif 'car' in header_lower and 'commercial' not in header_lower:
                standardized.append('cars')
            elif any(term in header_lower for term in ['commercial', 'truck', 'cv']):
                standardized.append('commercial_vehicles')
            elif 'total' in header_lower:
                standardized.append('total')
            elif any(term in header_lower for term in ['change', '%', 'percent']):
                standardized.append('percent_change')
            else:
                # Keep original header but clean it
                clean_header = header_lower.replace(' ', '_').replace('-', '_')
                standardized.append(clean_header)
        
        return standardized
    
    def _parse_table_row(self, cells, headers, year):
        """Parse individual table row"""
        row_data = {'year': year, 'source': 'OICA'}
        
        try:
            for i, cell in enumerate(cells):
                if i < len(headers):
                    header = headers[i]
                    value = cell.get_text(strip=True)
                    
                    # Clean and convert values based on column type
                    if header == 'country_region':
                        row_data[header] = value
                    elif header in ['cars', 'commercial_vehicles', 'total']:
                        cleaned_value = clean_numeric_value(value)
                        row_data[header] = cleaned_value
                    elif header == 'percent_change':
                        # Handle percentage values
                        cleaned_value = clean_numeric_value(value.replace('%', ''))
                        row_data[header] = cleaned_value
                    else:
                        # Try to clean as numeric, fallback to string
                        cleaned_value = clean_numeric_value(value)
                        row_data[header] = cleaned_value if cleaned_value is not None else value
            
        except Exception as e:
            self.logger.error(f"Error parsing table row: {e}")
            return None
        
        return row_data
    
    def _validate_row_data(self, row_data):
        """Validate row data quality"""
        # Check required fields
        required_fields = ['country_region', 'year']
        for field in required_fields:
            if field not in row_data or not row_data[field]:
                return False
        
        # Check that at least one numeric field has data
        numeric_fields = ['cars', 'commercial_vehicles', 'total']
        has_numeric_data = any(
            field in row_data and row_data[field] is not None 
            for field in numeric_fields
        )
        
        return has_numeric_data
    
    def _extract_from_text(self, soup, year):
        """Enhanced text extraction method"""
        data = []
        
        try:
            # Get all text content
            text_content = soup.get_text()
            lines = text_content.split('\n')
            
            # Look for production data patterns
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Check if line contains country production data
                if self._is_country_data_line(line):
                    parsed_row = self._parse_country_line(line, year)
                    if parsed_row and self._validate_row_data(parsed_row):
                        data.append(parsed_row)
            
            self.logger.info(f"Extracted {len(data)} rows from text for year {year}")
            
        except Exception as e:
            self.logger.error(f"Error extracting from text for year {year}: {e}")
        
        return data
    
    def _is_country_data_line(self, line):
        """Enhanced country data line detection"""
        parts = line.split()
        if len(parts) < 3:
            return False
        
        # Check for numeric data in the line
        numeric_parts = 0
        for part in parts:
            cleaned = part.replace(',', '').replace('%', '').replace('-', '').replace('+', '')
            if cleaned.replace('.', '').isdigit():
                numeric_parts += 1
        
        # Must have at least 2 numeric values and start with text (country name)
        return numeric_parts >= 2 and not parts[0].replace(',', '').replace('.', '').isdigit()
    
    def _parse_country_line(self, line, year):
        """Enhanced country line parsing"""
        parts = line.split()
        
        if len(parts) < 3:
            return None
        
        try:
            # Find where numeric data starts
            numeric_start = -1
            for i, part in enumerate(parts):
                cleaned = part.replace(',', '').replace('%', '').replace('-', '').replace('+', '')
                if cleaned.replace('.', '').isdigit():
                    numeric_start = i
                    break
            
            if numeric_start == -1:
                return None
            
            # Extract country name (everything before numeric data)
            country_parts = parts[:numeric_start]
            country = ' '.join(country_parts)
            
            # Extract numeric data
            numeric_parts = parts[numeric_start:]
            
            row_data = {
                'country_region': country,
                'year': year,
                'source': 'OICA'
            }
            
            # Map numeric values to appropriate fields
            if len(numeric_parts) >= 3:
                row_data['cars'] = clean_numeric_value(numeric_parts[0])
                row_data['commercial_vehicles'] = clean_numeric_value(numeric_parts[1])
                row_data['total'] = clean_numeric_value(numeric_parts[2])
                
                if len(numeric_parts) >= 4:
                    row_data['percent_change'] = clean_numeric_value(numeric_parts[3])
            
            return row_data
            
        except Exception as e:
            self.logger.error(f"Error parsing country line '{line}': {e}")
            return None
    
    def _find_download_links(self, soup, year):
        """Find and catalog download links for the year"""
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link['href']
            text = link.get_text(strip=True)
            
            # Check if link points to downloadable file
            download_extensions = ['.pdf', '.xlsx', '.xls', '.csv']
            for ext in download_extensions:
                if ext.lower() in href.lower():
                    full_url = urljoin(OICA_CONFIG['base_url'], href)
                    filename = get_file_name_from_url(full_url)
                    
                    self.download_links.append({
                        'url': full_url,
                        'filename': sanitize_filename(f"oica_{year}_{filename}"),
                        'type': ext.replace('.', ''),
                        'year': year,
                        'description': text,
                        'source': 'OICA'
                    })
    
    def download_files(self):
        """Download all found files"""
        if not self.download_links:
            self.logger.warning("No download links found")
            return []
        
        downloaded_files = []
        
        for link_info in self.download_links:
            try:
                response = make_request(link_info['url'])
                
                filepath = os.path.join(FILES_DIR, link_info['filename'])
                
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                self.logger.info(f"Downloaded: {link_info['filename']}")
                downloaded_files.append(filepath)
                
            except Exception as e:
                self.logger.error(f"Failed to download {link_info['filename']}: {e}")
        
        return downloaded_files
    
    def get_data_by_year(self, year):
        """Get data for specific year"""
        return [d for d in self.data if d.get('year') == year]
    
    def get_data_by_country(self, country):
        """Get data for specific country across all years"""
        return [d for d in self.data if country.lower() in d.get('country_region', '').lower()]
    
    def save_data(self, filename=None, format='both'):
        """Save scraped data with enhanced options"""
        if not self.data:
            self.logger.warning("No OICA data to save")
            return None
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"oica_production_enhanced_{timestamp}"
        else:
            base_filename = filename.replace('.json', '').replace('.csv', '')
        
        saved_files = []
        
        # Save as JSON
        if format in ['json', 'both']:
            json_path = os.path.join(DATA_DIR, f"{base_filename}.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, indent=2, ensure_ascii=False, default=str)
            self.logger.info(f"Data saved to: {json_path}")
            saved_files.append(json_path)
        
        # Save as CSV
        if format in ['csv', 'both']:
            csv_path = os.path.join(DATA_DIR, f"{base_filename}.csv")
            df = pd.DataFrame(self.data)
            df.to_csv(csv_path, index=False, encoding='utf-8')
            self.logger.info(f"Data saved to: {csv_path}")
            saved_files.append(csv_path)
        
        return saved_files
    
    def get_summary(self):
        """Get comprehensive summary of scraped data"""
        if not self.data:
            return "No OICA data available"
        
        df = pd.DataFrame(self.data)
        
        summary = {
            'source': 'OICA Enhanced',
            'total_records': len(df),
            'years_covered': sorted(df['year'].unique().tolist()) if 'year' in df.columns else [],
            'countries_covered': len(df['country_region'].unique()) if 'country_region' in df.columns else 0,
            'top_producers': self._get_top_producers(df),
            'total_global_production': self._get_global_totals(df),
            'download_links_found': len(self.download_links),
            'data_quality': self._assess_data_quality(df)
        }
        
        return summary
    
    def _get_top_producers(self, df, top_n=10):
        """Get top vehicle producing countries"""
        if 'total' not in df.columns or 'country_region' not in df.columns:
            return []
        
        # Get latest year data
        latest_year = df['year'].max()
        latest_data = df[df['year'] == latest_year]
        
        top_producers = latest_data.nlargest(top_n, 'total')[['country_region', 'total']].to_dict('records')
        return top_producers
    
    def _get_global_totals(self, df):
        """Calculate global production totals by year"""
        if 'total' not in df.columns or 'year' not in df.columns:
            return {}
        
        global_totals = df.groupby('year')['total'].sum().to_dict()
        return global_totals
    
    def _assess_data_quality(self, df):
        """Assess quality of scraped data"""
        quality_metrics = {
            'completeness': {},
            'consistency': {},
            'accuracy': {}
        }
        
        # Completeness check
        for col in ['country_region', 'cars', 'commercial_vehicles', 'total']:
            if col in df.columns:
                missing_pct = (df[col].isnull().sum() / len(df)) * 100
                quality_metrics['completeness'][col] = f"{100 - missing_pct:.1f}%"
        
        # Consistency check
        if all(col in df.columns for col in ['cars', 'commercial_vehicles', 'total']):
            # Check if total = cars + commercial_vehicles
            calculated_total = df['cars'].fillna(0) + df['commercial_vehicles'].fillna(0)
            consistency_check = (df['total'] == calculated_total).sum() / len(df) * 100
            quality_metrics['consistency']['total_calculation'] = f"{consistency_check:.1f}%"
        
        return quality_metrics
