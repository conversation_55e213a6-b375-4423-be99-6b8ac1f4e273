"""
Data Consolidator for Multi-Source Automotive Data
Consolidates data from all sources into unified datasets
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import sys

from enhanced_config import DATA_DIR, VALIDATION_CONFIG
from data_sources import <PERSON><PERSON>ced<PERSON><PERSON><PERSON><PERSON>raper, USTRScraper, MaterialsScraper, AutomakerScraper

class AutomotiveDataConsolidator:
    """Consolidates automotive data from multiple sources"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.consolidated_data = {}
        self.data_quality_report = {}
        
        # Initialize scrapers
        self.oica_scraper = EnhancedOICAScraper()
        self.ustr_scraper = USTRScraper()
        self.materials_scraper = MaterialsScraper()
        self.automaker_scraper = AutomakerScraper()
    
    def collect_all_data(self, years=None, download_files=False):
        """Collect data from all sources"""
        if years is None:
            years = [datetime.now().year - i for i in range(3)]  # Last 3 years
        
        self.logger.info(f"Starting data collection for years: {years}")
        
        # Collect OICA production data
        self.logger.info("Collecting OICA production data...")
        try:
            oica_data = self.oica_scraper.scrape_multiple_years(years)
            self.consolidated_data['oica'] = oica_data
            if download_files:
                self.oica_scraper.download_files()
        except Exception as e:
            self.logger.error(f"Failed to collect OICA data: {e}")
            self.consolidated_data['oica'] = []
        
        # Collect US trade data
        self.logger.info("Collecting US trade data...")
        try:
            ustr_data = self.ustr_scraper.scrape_automotive_trade_data(years)
            self.consolidated_data['trade'] = ustr_data
        except Exception as e:
            self.logger.error(f"Failed to collect trade data: {e}")
            self.consolidated_data['trade'] = []
        
        # Collect materials pricing data
        self.logger.info("Collecting materials pricing data...")
        try:
            materials_data = self.materials_scraper.scrape_all_materials_data()
            self.consolidated_data['materials'] = materials_data
        except Exception as e:
            self.logger.error(f"Failed to collect materials data: {e}")
            self.consolidated_data['materials'] = []
        
        # Collect automaker data
        self.logger.info("Collecting automaker reports data...")
        try:
            automaker_data = self.automaker_scraper.scrape_all_automakers(years)
            self.consolidated_data['automaker'] = automaker_data
            if download_files:
                self.automaker_scraper.download_reports()
        except Exception as e:
            self.logger.error(f"Failed to collect automaker data: {e}")
            self.consolidated_data['automaker'] = []
        
        # Validate and clean data
        self._validate_all_data()
        
        return self.consolidated_data
    
    def _validate_all_data(self):
        """Validate data quality across all sources"""
        self.logger.info("Validating data quality...")
        
        for source_name, data in self.consolidated_data.items():
            try:
                quality_report = self._validate_source_data(source_name, data)
                self.data_quality_report[source_name] = quality_report
            except Exception as e:
                self.logger.error(f"Error validating {source_name} data: {e}")
                self.data_quality_report[source_name] = {'status': 'error', 'message': str(e)}
    
    def _validate_source_data(self, source_name, data):
        """Validate data for a specific source"""
        if not data:
            return {'status': 'empty', 'records': 0}
        
        df = pd.DataFrame(data)
        
        # Get validation config for this source
        required_fields = VALIDATION_CONFIG['required_fields'].get(source_name, [])
        min_records = VALIDATION_CONFIG['data_quality_checks']['min_records_per_source']
        max_missing_pct = VALIDATION_CONFIG['data_quality_checks']['max_missing_percentage']
        
        quality_report = {
            'status': 'valid',
            'records': len(df),
            'issues': []
        }
        
        # Check minimum records
        if len(df) < min_records:
            quality_report['issues'].append(f"Insufficient records: {len(df)} < {min_records}")
            quality_report['status'] = 'warning'
        
        # Check required fields
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            quality_report['issues'].append(f"Missing required fields: {missing_fields}")
            quality_report['status'] = 'warning'
        
        # Check missing data percentage
        for field in required_fields:
            if field in df.columns:
                missing_pct = (df[field].isnull().sum() / len(df)) * 100
                if missing_pct > max_missing_pct:
                    quality_report['issues'].append(f"High missing data in {field}: {missing_pct:.1f}%")
                    quality_report['status'] = 'warning'
        
        # Detect duplicates
        if VALIDATION_CONFIG['data_quality_checks']['duplicate_detection']:
            duplicates = df.duplicated().sum()
            if duplicates > 0:
                quality_report['issues'].append(f"Found {duplicates} duplicate records")
                quality_report['status'] = 'warning'
        
        # Outlier detection for numeric fields
        if VALIDATION_CONFIG['data_quality_checks']['outlier_detection']:
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                outliers = self._detect_outliers(df[col])
                if outliers > 0:
                    quality_report['issues'].append(f"Found {outliers} outliers in {col}")
        
        return quality_report
    
    def _detect_outliers(self, series):
        """Detect outliers using IQR method"""
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = ((series < lower_bound) | (series > upper_bound)).sum()
        return outliers
    
    def create_unified_datasets(self):
        """Create unified datasets for analysis"""
        unified_datasets = {}
        
        # Create production dataset
        unified_datasets['production'] = self._create_production_dataset()
        
        # Create trade dataset
        unified_datasets['trade'] = self._create_trade_dataset()
        
        # Create materials dataset
        unified_datasets['materials'] = self._create_materials_dataset()
        
        # Create financial dataset
        unified_datasets['financial'] = self._create_financial_dataset()
        
        # Create market analysis dataset
        unified_datasets['market_analysis'] = self._create_market_analysis_dataset()
        
        return unified_datasets
    
    def _create_production_dataset(self):
        """Create unified production dataset"""
        if not self.consolidated_data.get('oica'):
            return pd.DataFrame()
        
        df = pd.DataFrame(self.consolidated_data['oica'])
        
        # Standardize columns
        standard_columns = {
            'country_region': 'country',
            'cars': 'passenger_vehicles',
            'commercial_vehicles': 'commercial_vehicles',
            'total': 'total_production',
            'percent_change': 'yoy_change_percent',
            'year': 'year'
        }
        
        df = df.rename(columns=standard_columns)
        
        # Add calculated fields
        if 'passenger_vehicles' in df.columns and 'commercial_vehicles' in df.columns:
            df['passenger_vehicle_share'] = (df['passenger_vehicles'] / df['total_production'] * 100).round(2)
            df['commercial_vehicle_share'] = (df['commercial_vehicles'] / df['total_production'] * 100).round(2)
        
        return df
    
    def _create_trade_dataset(self):
        """Create unified trade dataset"""
        if not self.consolidated_data.get('trade'):
            return pd.DataFrame()
        
        df = pd.DataFrame(self.consolidated_data['trade'])
        
        # Standardize columns
        standard_columns = {
            'country': 'country',
            'year': 'year',
            'value': 'trade_value_usd',
            'metric': 'trade_type'
        }
        
        df = df.rename(columns={k: v for k, v in standard_columns.items() if k in df.columns})
        
        return df
    
    def _create_materials_dataset(self):
        """Create unified materials pricing dataset"""
        if not self.consolidated_data.get('materials'):
            return pd.DataFrame()
        
        df = pd.DataFrame(self.consolidated_data['materials'])
        
        # Convert date column
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        
        # Add automotive relevance scoring
        relevance_scores = {
            'chassis_body': 5,
            'body_panels': 4,
            'wiring_electronics': 4,
            'batteries': 5,
            'tires': 4,
            'catalytic_converters': 3,
            'fuel_plastics': 3,
            'manufacturing_energy': 2,
            'general': 1
        }
        
        df['automotive_relevance_score'] = df['automotive_use'].map(relevance_scores).fillna(1)
        
        return df
    
    def _create_financial_dataset(self):
        """Create unified financial dataset"""
        if not self.consolidated_data.get('automaker'):
            return pd.DataFrame()
        
        df = pd.DataFrame(self.consolidated_data['automaker'])
        
        # Standardize financial metrics
        metric_mapping = {
            'revenue': 'revenue_usd',
            'net_income': 'net_income_usd',
            'production': 'production_units',
            'sales_volume': 'sales_units',
            'market_share': 'market_share_percent',
            'electric_vehicles': 'ev_units'
        }
        
        # Create separate rows for each metric
        financial_data = []
        for _, row in df.iterrows():
            metric = row.get('metric', '')
            if metric in metric_mapping:
                financial_data.append({
                    'company': row.get('company', ''),
                    'year': row.get('year', ''),
                    'metric_type': metric_mapping[metric],
                    'value': row.get('value', 0),
                    'unit': row.get('unit', ''),
                    'source': row.get('source', '')
                })
        
        return pd.DataFrame(financial_data)
    
    def _create_market_analysis_dataset(self):
        """Create market analysis dataset combining multiple sources"""
        analysis_data = []
        
        # Get production data by country and year
        if self.consolidated_data.get('oica'):
            production_df = pd.DataFrame(self.consolidated_data['oica'])
            
            for _, row in production_df.iterrows():
                analysis_data.append({
                    'country': row.get('country_region', ''),
                    'year': row.get('year', ''),
                    'metric_type': 'production',
                    'value': row.get('total', 0),
                    'unit': 'vehicles',
                    'source': 'OICA'
                })
        
        # Add trade data
        if self.consolidated_data.get('trade'):
            trade_df = pd.DataFrame(self.consolidated_data['trade'])
            
            for _, row in trade_df.iterrows():
                analysis_data.append({
                    'country': row.get('country', ''),
                    'year': row.get('year', ''),
                    'metric_type': f"trade_{row.get('metric', 'value')}",
                    'value': row.get('value', 0),
                    'unit': 'USD',
                    'source': 'Trade Data'
                })
        
        return pd.DataFrame(analysis_data)
    
    def save_consolidated_data(self, filename=None):
        """Save all consolidated data"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"automotive_data_consolidated_{timestamp}.json"
        
        filepath = os.path.join(DATA_DIR, filename)
        
        # Prepare data for JSON serialization
        json_data = {
            'metadata': {
                'collection_date': datetime.now().isoformat(),
                'sources': list(self.consolidated_data.keys()),
                'data_quality': self.data_quality_report
            },
            'data': self.consolidated_data
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"Consolidated data saved to: {filepath}")
        return filepath
    
    def get_collection_summary(self):
        """Get summary of data collection"""
        summary = {
            'collection_date': datetime.now().isoformat(),
            'sources_collected': len(self.consolidated_data),
            'total_records': sum(len(data) for data in self.consolidated_data.values()),
            'data_quality': self.data_quality_report,
            'source_summaries': {}
        }
        
        # Get individual source summaries
        if hasattr(self.oica_scraper, 'get_summary'):
            summary['source_summaries']['oica'] = self.oica_scraper.get_summary()
        
        if hasattr(self.ustr_scraper, 'get_summary'):
            summary['source_summaries']['trade'] = self.ustr_scraper.get_summary()
        
        if hasattr(self.materials_scraper, 'get_summary'):
            summary['source_summaries']['materials'] = self.materials_scraper.get_summary()
        
        if hasattr(self.automaker_scraper, 'get_summary'):
            summary['source_summaries']['automaker'] = self.automaker_scraper.get_summary()
        
        return summary
