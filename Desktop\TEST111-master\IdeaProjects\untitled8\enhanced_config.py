"""
Enhanced Configuration file for Multi-Source Automotive Data Scraper
"""

import os
from datetime import datetime

# Base directories
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
OUTPUT_DIR = os.path.join(BASE_DIR, "output")
DATA_DIR = os.path.join(OUTPUT_DIR, "data")
FILES_DIR = os.path.join(OUTPUT_DIR, "files")
LOGS_DIR = os.path.join(OUTPUT_DIR, "logs")
EXCEL_DIR = os.path.join(OUTPUT_DIR, "excel")

# Create directories if they don't exist
for directory in [OUTPUT_DIR, DATA_DIR, FILES_DIR, LOGS_DIR, EXCEL_DIR]:
    os.makedirs(directory, exist_ok=True)

# =============================================================================
# OICA Configuration (existing)
# =============================================================================
OICA_CONFIG = {
    'base_url': "https://www.oica.net",
    'production_stats_base': "https://www.oica.net/category/production-statistics",
    'available_years': [
        2024, 2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015,
        2014, 2013, 2012, 2011, 2010, 2009, 2008, 2007, 2006, 2005,
        2004, 2003, 2002, 2001, 2000, 1999
    ],
    'default_year': 2019,
    'table_columns': {
        'Country/Region': 'country_region',
        'Cars': 'cars',
        'Commercial vehicles': 'commercial_vehicles', 
        'Total': 'total',
        '% change': 'percent_change'
    }
}

# =============================================================================
# U.S. Trade Data Configuration (USTR, WTO)
# =============================================================================
USTR_CONFIG = {
    'base_url': "https://ustr.gov",
    'trade_data_url': "https://ustr.gov/trade-agreements/free-trade-agreements",
    'automotive_section': "/automotive",
    'file_extensions': ['.pdf', '.xlsx', '.xls', '.csv'],
    'search_terms': ['automotive', 'vehicle', 'auto', 'car', 'truck'],
    'data_types': ['trade_balance', 'imports', 'exports', 'tariffs']
}

WTO_CONFIG = {
    'base_url': "https://www.wto.org",
    'trade_statistics_url': "https://www.wto.org/english/res_e/statis_e/statis_e.htm",
    'automotive_codes': ['8703', '8704', '8705', '8706', '8707', '8708'],  # HS codes for vehicles
    'data_endpoints': [
        '/english/res_e/statis_e/its_e.htm',
        '/english/res_e/statis_e/merch_trade_stat_e.htm'
    ]
}

# =============================================================================
# Raw Materials Pricing Configuration
# =============================================================================
MATERIALS_CONFIG = {
    'sources': {
        'lme': {  # London Metal Exchange
            'base_url': "https://www.lme.com",
            'data_url': "/en/metals/non-ferrous",
            'metals': ['aluminum', 'copper', 'steel', 'nickel', 'zinc'],
            'automotive_relevance': {
                'aluminum': 'body_panels',
                'copper': 'wiring',
                'steel': 'chassis',
                'nickel': 'batteries',
                'zinc': 'coating'
            }
        },
        'investing': {  # Investing.com commodities
            'base_url': "https://www.investing.com",
            'commodities_url': "/commodities",
            'materials': ['crude-oil', 'natural-gas', 'rubber', 'platinum', 'palladium'],
            'automotive_relevance': {
                'crude-oil': 'fuel_plastics',
                'natural-gas': 'manufacturing_energy',
                'rubber': 'tires',
                'platinum': 'catalytic_converters',
                'palladium': 'catalytic_converters'
            }
        },
        'fred': {  # Federal Reserve Economic Data
            'base_url': "https://fred.stlouisfed.org",
            'api_key_required': True,
            'series_ids': {
                'steel_price': 'DSTLUS',
                'aluminum_price': 'PALUMUSDM',
                'copper_price': 'PCOPPUSDM',
                'oil_price': 'DCOILWTICO'
            }
        }
    }
}

# =============================================================================
# Automaker Reports Configuration
# =============================================================================
AUTOMAKER_CONFIG = {
    'companies': {
        'toyota': {
            'name': 'Toyota Motor Corporation',
            'investor_url': 'https://global.toyota/en/ir/',
            'annual_reports_section': '/library/annual/',
            'quarterly_reports_section': '/library/quarterly/',
            'search_terms': ['annual report', 'sustainability report', 'investor presentation']
        },
        'volkswagen': {
            'name': 'Volkswagen Group',
            'investor_url': 'https://www.volkswagenag.com/en/InvestorRelations.html',
            'annual_reports_section': '/annual-reports/',
            'quarterly_reports_section': '/quarterly-reports/',
            'search_terms': ['annual report', 'interim report', 'investor presentation']
        },
        'gm': {
            'name': 'General Motors',
            'investor_url': 'https://investor.gm.com/',
            'annual_reports_section': '/financial-reports/annual-reports',
            'quarterly_reports_section': '/financial-reports/quarterly-reports',
            'search_terms': ['annual report', '10-K', 'investor presentation']
        },
        'ford': {
            'name': 'Ford Motor Company',
            'investor_url': 'https://corporate.ford.com/investors.html',
            'annual_reports_section': '/reports-and-filings/annual-reports/',
            'quarterly_reports_section': '/reports-and-filings/quarterly-reports/',
            'search_terms': ['annual report', '10-K', 'sustainability report']
        },
        'stellantis': {
            'name': 'Stellantis N.V.',
            'investor_url': 'https://www.stellantis.com/en/investors',
            'annual_reports_section': '/financial-reports/annual-reports',
            'quarterly_reports_section': '/financial-reports/quarterly-reports',
            'search_terms': ['annual report', 'universal registration document']
        }
    },
    'data_extraction_targets': [
        'production_volumes',
        'sales_figures',
        'revenue',
        'market_share',
        'regional_performance',
        'electric_vehicle_data',
        'sustainability_metrics'
    ]
}

# =============================================================================
# General Request Settings
# =============================================================================
REQUEST_CONFIG = {
    'timeout': 30,
    'delay': 1,  # seconds between requests
    'max_retries': 3,
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
}

# =============================================================================
# File Processing Configuration
# =============================================================================
FILE_CONFIG = {
    'download_extensions': ['.pdf', '.xlsx', '.xls', '.csv', '.json'],
    'max_file_size': 100 * 1024 * 1024,  # 100MB
    'allowed_content_types': [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv',
        'application/json'
    ]
}

# =============================================================================
# Excel Output Configuration
# =============================================================================
EXCEL_CONFIG = {
    'filename_template': 'automotive_data_consolidated_{timestamp}.xlsx',
    'sheets': {
        'summary': 'Executive Summary',
        'oica_production': 'OICA Production Data',
        'us_trade': 'US Trade Data',
        'materials_pricing': 'Raw Materials Pricing',
        'automaker_reports': 'Automaker Financial Data',
        'market_analysis': 'Market Analysis',
        'trends': 'Industry Trends'
    },
    'formatting': {
        'header_style': {
            'bold': True,
            'bg_color': '#4472C4',
            'font_color': 'white',
            'border': 1
        },
        'data_style': {
            'border': 1,
            'align': 'center'
        },
        'number_format': '#,##0',
        'percentage_format': '0.00%',
        'currency_format': '$#,##0.00'
    }
}

# =============================================================================
# Logging Configuration
# =============================================================================
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': os.path.join(LOGS_DIR, f'scraper_{datetime.now().strftime("%Y%m%d")}.log'),
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# =============================================================================
# Data Validation Configuration
# =============================================================================
VALIDATION_CONFIG = {
    'required_fields': {
        'oica': ['country_region', 'year', 'total'],
        'trade': ['country', 'year', 'value'],
        'materials': ['material', 'date', 'price'],
        'automaker': ['company', 'year', 'metric', 'value']
    },
    'data_quality_checks': {
        'min_records_per_source': 10,
        'max_missing_percentage': 20,
        'outlier_detection': True,
        'duplicate_detection': True
    }
}

# =============================================================================
# Unified Excel Output Configuration
# =============================================================================
UNIFIED_EXCEL_CONFIG = {
    'filename_template': 'automotive_unified_data_{timestamp}.xlsx',
    'target_columns': [
        'Year',
        'Country',
        'Manufacturer',
        'VehicleType',
        'ProductionVolume',
        'TariffRate_USA',
        'SteelPrice',
        'EV_Share',
        'ImportVolume'
    ],
    'column_formats': {
        'Year': 'integer',
        'Country': 'text',
        'Manufacturer': 'text',
        'VehicleType': 'text',
        'ProductionVolume': 'number_thousands',
        'TariffRate_USA': 'percentage',
        'SteelPrice': 'currency',
        'EV_Share': 'percentage',
        'ImportVolume': 'number_thousands'
    },
    'sample_data': {
        'Year': 2023,
        'Country': 'Japan',
        'Manufacturer': 'Toyota',
        'VehicleType': 'Passenger',
        'ProductionVolume': 8700000,
        'TariffRate_USA': 0.025,
        'SteelPrice': 720,
        'EV_Share': 0.24,
        'ImportVolume': 1200000
    }
}

# =============================================================================
# Manufacturer Mapping Configuration
# =============================================================================
MANUFACTURER_MAPPING = {
    'country_to_manufacturers': {
        'Japan': ['Toyota', 'Honda', 'Nissan', 'Mazda', 'Subaru', 'Mitsubishi', 'Suzuki'],
        'Germany': ['Volkswagen', 'BMW', 'Mercedes-Benz', 'Audi', 'Porsche'],
        'USA': ['General Motors', 'Ford', 'Chrysler', 'Tesla'],
        'South Korea': ['Hyundai', 'Kia'],
        'France': ['Renault', 'Peugeot', 'Citroën'],
        'Italy': ['Fiat', 'Ferrari', 'Lamborghini'],
        'China': ['BYD', 'Geely', 'SAIC', 'Great Wall', 'Chery'],
        'India': ['Tata Motors', 'Mahindra', 'Maruti Suzuki'],
        'United Kingdom': ['Jaguar Land Rover', 'Aston Martin', 'McLaren'],
        'Sweden': ['Volvo'],
        'Czech Republic': ['Škoda']
    },
    'manufacturer_ev_share': {
        'Tesla': 0.95,
        'BYD': 0.85,
        'Volkswagen': 0.15,
        'Toyota': 0.24,
        'General Motors': 0.08,
        'Ford': 0.12,
        'BMW': 0.18,
        'Mercedes-Benz': 0.16,
        'Hyundai': 0.22,
        'Kia': 0.20,
        'Nissan': 0.14,
        'Honda': 0.10,
        'Audi': 0.19,
        'Volvo': 0.35,
        'Peugeot': 0.13,
        'Renault': 0.17
    }
}

# =============================================================================
# Trade Data Configuration
# =============================================================================
TRADE_DATA_CONFIG = {
    'usa_tariff_rates': {
        'Japan': 0.025,
        'Germany': 0.025,
        'South Korea': 0.025,
        'United Kingdom': 0.025,
        'Italy': 0.025,
        'France': 0.025,
        'Sweden': 0.025,
        'China': 0.275,  # Higher tariff
        'Mexico': 0.0,   # NAFTA/USMCA
        'Canada': 0.0,   # NAFTA/USMCA
        'India': 0.025,
        'Thailand': 0.025,
        'Turkey': 0.025,
        'Brazil': 0.025,
        'default': 0.025
    },
    'vehicle_types': {
        'passenger_cars': 'Passenger',
        'commercial_vehicles': 'Commercial',
        'electric_vehicles': 'Electric',
        'hybrid_vehicles': 'Hybrid'
    }
}

# =============================================================================
# Steel Price Data (Sample/Default values)
# =============================================================================
STEEL_PRICE_DATA = {
    'monthly_prices_2023': {
        1: 680, 2: 690, 3: 710, 4: 720, 5: 730, 6: 725,
        7: 715, 8: 720, 9: 735, 10: 740, 11: 745, 12: 750
    },
    'monthly_prices_2024': {
        1: 755, 2: 760, 3: 765, 4: 770, 5: 775, 6: 780,
        7: 785, 8: 790, 9: 795, 10: 800, 11: 805, 12: 810
    },
    'default_price': 720
}
