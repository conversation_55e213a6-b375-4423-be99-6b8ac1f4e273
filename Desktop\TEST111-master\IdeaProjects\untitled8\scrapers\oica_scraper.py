"""
OICA Production Statistics Scraper
Scrapes vehicle production data from OICA (Organisation Internationale des Constructeurs d'Automobiles)
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import logging
import re
from typing import Dict, List, Optional
from urllib.parse import urljoin

class OICAScraper:
    """Scraper for OICA vehicle production statistics"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_url = "https://www.oica.net"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def get_production_data(self, years: List[int]) -> List[Dict]:
        """
        Get vehicle production data for specified years
        
        Args:
            years: List of years to scrape data for
            
        Returns:
            List of dictionaries containing production data
        """
        all_data = []
        
        for year in years:
            self.logger.info(f"Scraping OICA data for year {year}")
            try:
                year_data = self._scrape_year_data(year)
                all_data.extend(year_data)
                time.sleep(1)  # Be respectful to the server
            except Exception as e:
                self.logger.error(f"Failed to scrape data for year {year}: {e}")
                
        return all_data
    
    def _scrape_year_data(self, year: int) -> List[Dict]:
        """Scrape production data for a specific year"""
        
        # OICA statistics URLs (these may need to be updated based on actual site structure)
        urls_to_try = [
            f"{self.base_url}/category/production-statistics/{year}/",
            f"{self.base_url}/category/production-statistics/",
            f"{self.base_url}/production-statistics-{year}/",
        ]
        
        for url in urls_to_try:
            try:
                response = self.session.get(url, timeout=30)
                if response.status_code == 200:
                    return self._parse_production_page(response.text, year)
            except Exception as e:
                self.logger.warning(f"Failed to access {url}: {e}")
                continue
                
        # If direct URLs don't work, try to find the data through navigation
        return self._find_production_data_by_navigation(year)
    
    def _parse_production_page(self, html: str, year: int) -> List[Dict]:
        """Parse production data from HTML page"""
        soup = BeautifulSoup(html, 'html.parser')
        data = []
        
        # Look for tables containing production data
        tables = soup.find_all('table')
        
        for table in tables:
            # Check if this table contains production data
            headers = [th.get_text().strip().lower() for th in table.find_all('th')]
            
            if any(keyword in ' '.join(headers) for keyword in ['country', 'production', 'vehicles', 'cars']):
                data.extend(self._parse_production_table(table, year))
        
        return data
    
    def _parse_production_table(self, table, year: int) -> List[Dict]:
        """Parse a production data table"""
        data = []
        
        # Get headers
        header_row = table.find('tr')
        if not header_row:
            return data
            
        headers = [th.get_text().strip() for th in header_row.find_all(['th', 'td'])]
        
        # Process data rows
        rows = table.find_all('tr')[1:]  # Skip header row
        
        for row in rows:
            cells = [td.get_text().strip() for td in row.find_all(['td', 'th'])]
            
            if len(cells) >= 2:  # At least country and one production value
                country = cells[0]
                
                # Extract production values
                production_values = []
                for cell in cells[1:]:
                    # Clean numeric values
                    cleaned = re.sub(r'[^\d,.]', '', cell)
                    if cleaned:
                        try:
                            # Handle different number formats
                            value = cleaned.replace(',', '')
                            production_values.append(int(float(value)))
                        except ValueError:
                            production_values.append(0)
                    else:
                        production_values.append(0)
                
                # Create data entry
                if production_values:
                    data.append({
                        'Year': year,
                        'Country': country,
                        'Manufacturer': 'Various',  # OICA data is by country, not manufacturer
                        'VehicleType': 'Total',
                        'ProductionVolume': production_values[0] if production_values else 0,
                        'TariffRate_USA': None,  # Will be filled by trade scraper
                        'SteelPrice': None,  # Will be filled by materials scraper
                        'EV_Share': None,  # Will be filled by EV scraper
                        'ImportVolume': None  # Will be filled by trade scraper
                    })
        
        return data
    
    def _find_production_data_by_navigation(self, year: int) -> List[Dict]:
        """Try to find production data by navigating the site"""
        try:
            # Get main statistics page
            response = self.session.get(f"{self.base_url}/category/production-statistics/")
            if response.status_code != 200:
                return []
                
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for links containing the year
            year_links = []
            for link in soup.find_all('a', href=True):
                if str(year) in link.get_text() or str(year) in link['href']:
                    year_links.append(urljoin(self.base_url, link['href']))
            
            # Try each year link
            for link in year_links:
                try:
                    response = self.session.get(link, timeout=30)
                    if response.status_code == 200:
                        data = self._parse_production_page(response.text, year)
                        if data:
                            return data
                except Exception as e:
                    self.logger.warning(f"Failed to process link {link}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Navigation search failed: {e}")
            
        return []
    
    def get_available_years(self) -> List[int]:
        """Get list of available years for production data"""
        try:
            response = self.session.get(f"{self.base_url}/category/production-statistics/")
            if response.status_code != 200:
                return list(range(2014, 2025))  # Default range
                
            soup = BeautifulSoup(response.text, 'html.parser')
            years = set()
            
            # Look for year mentions in links and text
            for element in soup.find_all(['a', 'span', 'div']):
                text = element.get_text()
                year_matches = re.findall(r'\b(20[0-2][0-9])\b', text)
                years.update(int(year) for year in year_matches)
            
            return sorted(list(years), reverse=True)
            
        except Exception as e:
            self.logger.error(f"Failed to get available years: {e}")
            return list(range(2014, 2025))  # Default range
